import os
import math
import decimal
from decimal import Decimal, ROUND_HALF_UP
from google.cloud import bigquery
import pandas as pd
import gspread
from dateutil.relativedelta import relativedelta

def col_letter_to_index(letter):
    """
    Convert an Excel-style column letter (e.g., "A", "B", "AA")
    to a 1-indexed column number.
    """
    letter = letter.upper().strip()
    index = 0
    for char in letter:
        index = index * 26 + (ord(char) - ord('A') + 1)
    return index

def safe_json_value(val):
    """
    Replace NaN or None with an empty string so it's JSON-compliant.
    If you prefer numeric zero instead, change '' to 0.
    """
    if val is None or (isinstance(val, float) and math.isnan(val)):
        return ""
    return val

def fix_commission(raw_val):
    """
    Interprets raw_val as an integer/float meaning "X percent", e.g. 20 => 20%.

    Steps:
      1) Convert to Decimal.
      2) While < 2, multiply by 10  (e.g. 1.5 => 15).
      3) While > 30, divide by 10   (e.g. 38 => 3.8).
      4) Round the result to 1 decimal place (e.g. 3.75 => 3.8).
      5) Convert that final "percentage" to a fraction by dividing by 100
         so that Google Sheets "0.0%" formatting shows the correct display.

    Examples:
      - raw = 1.5 => <2 => *10 => 15 => 15.0 => stored as 0.15 => displayed as 15.0%
      - raw = 38 => >30 => /10 => 3.8 => stored as 0.038 => displayed as 3.8%
      - raw = 163 => >30 => /10 => 16.3 => stored as 0.163 => displayed as 16.3%
      - raw = 0.15 => <2 => *10 => 1.5 => still <2 => *10 => 15 => stored as 0.15 => 15.0%
    """
    if raw_val == "" or raw_val is None:
        return ""
    try:
        dec = Decimal(str(raw_val))

        # While < 2, multiply by 10
        while dec < Decimal('2'):
            dec *= Decimal('10')

        # While > 30, divide by 10
        while dec > Decimal('30'):
            dec /= Decimal('10')

        # Round to 1 decimal place, half up
        dec_1dp = dec.quantize(Decimal('0.1'), rounding=ROUND_HALF_UP)

        # Now we must store the fraction in the sheet => divide by 100
        fraction_value = dec_1dp / Decimal('100')

        return float(fraction_value)  # e.g. 0.163 for 16.3%
    except:
        return ""

def main():
    # ----- Step 1. User Input -----
    kol_input = input("Enter KOL name to filter (or type 'all' for all tables): ").strip()
    if kol_input.lower() == 'all' or kol_input == '':
        target_kolname = None
    else:
        target_kolname = kol_input

    sheet_link = input("Enter the Google Sheet URL: ").strip()

    gc = gspread.oauth()
    try:
        sheet = gc.open_by_url(sheet_link)
    except Exception as e:
        print("Error opening the Google Sheet:", e)
        return

    worksheets = sheet.worksheets()
    if len(worksheets) == 1:
        worksheet = worksheets[0]
        print(f"Only one worksheet found. Using worksheet: '{worksheet.title}'")
    else:
        worksheet_name = input("Enter the worksheet name (tab) within the Google Sheet: ").strip()
        try:
            worksheet = sheet.worksheet(worksheet_name)
        except Exception as e:
            print("Error accessing the specified worksheet:", e)
            return

    shop_id_col_letter = input("Enter the column letter that contains shop IDs (e.g., A): ").strip()
    shop_id_col_index = col_letter_to_index(shop_id_col_letter)

    # GMV columns
    avg_gmv_col_letter = input("Enter the column letter to put AVERAGE GMV (leave blank to skip): ").strip()
    if avg_gmv_col_letter == '':
        avg_gmv_col_letter = None

    max_gmv_col_letter = input("Enter the column letter to put MAX GMV (leave blank to skip): ").strip()
    if max_gmv_col_letter == '':
        max_gmv_col_letter = None

    min_gmv_col_letter = input("Enter the column letter to put MIN GMV (leave blank to skip): ").strip()
    if min_gmv_col_letter == '':
        min_gmv_col_letter = None

    # CMS columns (max, avg, min)
    max_cms_col_letter = input("Enter the column letter to put MAX CMS (leave blank to skip): ").strip()
    if max_cms_col_letter == '':
        max_cms_col_letter = None

    avg_cms_col_letter = input("Enter the column letter to put AVERAGE CMS (leave blank to skip): ").strip()
    if avg_cms_col_letter == '':
        avg_cms_col_letter = None

    min_cms_col_letter = input("Enter the column letter to put MIN CMS (leave blank to skip): ").strip()
    if min_cms_col_letter == '':
        min_cms_col_letter = None

    # ----- Step 2. BigQuery Client -----
    bq_client = bigquery.Client(project='beyondk-live-data')

    # 2a. Retrieve GMV data (6 months average instead of all time)
    if target_kolname:
        table_names_query = f"""
            SELECT table_name
            FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
            WHERE table_name LIKE '%_{target_kolname}'
        """
    else:
        table_names_query = """
            SELECT table_name
            FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
        """

    table_names_df = bq_client.query(table_names_query).to_dataframe()
    if table_names_df.empty:
        print("No matching campaign tables found in the Sales_data dataset.")
        df_gmv = pd.DataFrame(columns=['shop_id','shop_name','avg_gmv','max_gmv','min_gmv'])
    else:
        # Filter tables to last 6 months only
        from datetime import datetime

        def parse_sales_table_date(table_name):
            """Parse date from table name format: YYYY_MM_DD_KOLname"""
            parts = table_name.split("_")
            if len(parts) < 4:
                return None
            yyyy, mm, dd = parts[0], parts[1], parts[2]
            try:
                return datetime(year=int(yyyy), month=int(mm), day=int(dd))
            except ValueError:
                return None

        def filter_tables_for_6_months(table_names):
            """Filter tables to last 6 months from current date"""
            from dateutil.relativedelta import relativedelta

            now = datetime.now()
            # Calculate 6 months ago from current month
            start_of_range = now.replace(day=1) - relativedelta(months=5)  # 6 months including current
            end_of_range = now

            results = []
            for name in table_names:
                d = parse_sales_table_date(name)
                if d is not None and (start_of_range <= d <= end_of_range):
                    results.append(name)
            return results

        # Filter to last 6 months only
        all_tables = table_names_df['table_name'].tolist()
        filtered_tables = filter_tables_for_6_months(all_tables)

        if not filtered_tables:
            print("No campaign tables found in the last 6 months.")
            df_gmv = pd.DataFrame(columns=['shop_id','shop_name','avg_gmv','max_gmv','min_gmv'])
        else:
            print(f"Using {len(filtered_tables)} tables from last 6 months for AVG GMV calculation")
            union_queries = []
            for table in filtered_tables:
                union_queries.append(
                    f"""
                    SELECT shop_id, shop_name, SUM(total_gmv) AS campaign_gmv
                    FROM `beyondk-live-data.Sales_data.{table}`
                    GROUP BY shop_id, shop_name
                    """
                )
            union_query = " UNION ALL ".join(union_queries)

            final_query = f"""
            WITH union_data AS (
                {union_query}
            )
            SELECT
                shop_id,
                shop_name,
                AVG(campaign_gmv) AS avg_gmv,
                MAX(campaign_gmv) AS max_gmv,
                MIN(campaign_gmv) AS min_gmv
            FROM union_data
            GROUP BY shop_id, shop_name
            ORDER BY shop_id
            """
            df_gmv = bq_client.query(final_query).to_dataframe()

    print("BigQuery GMV data retrieved (first few rows):")
    print(df_gmv.head())

    # 2b. Retrieve (max, avg, min) CMS
    do_cms = any([max_cms_col_letter, avg_cms_col_letter, min_cms_col_letter])
    if do_cms:
        if target_kolname:
            price_table_names_query = f"""
                SELECT table_name
                FROM `beyondk-live-data.price_data.INFORMATION_SCHEMA.TABLES`
                WHERE table_name LIKE '%_{target_kolname}'
            """
        else:
            price_table_names_query = """
                SELECT table_name
                FROM `beyondk-live-data.price_data.INFORMATION_SCHEMA.TABLES`
            """

        price_table_names_df = bq_client.query(price_table_names_query).to_dataframe()
        if price_table_names_df.empty:
            print("No matching tables found in the price_data dataset for CMS calculation.")
            df_price = pd.DataFrame(columns=['shop_id','max_cms','avg_cms','min_cms'])
        else:
            union_queries_price = []
            for table in price_table_names_df['table_name']:
                union_queries_price.append(
                    f"SELECT shop_id, cms FROM `beyondk-live-data.price_data.{table}`"
                )
            price_union_query = " UNION ALL ".join(union_queries_price)

            price_final_query = f"""
            WITH union_price_data AS (
                {price_union_query}
            )
            SELECT
                CAST(shop_id AS STRING) AS shop_id,
                MAX(cms) AS max_cms,
                AVG(cms) AS avg_cms,
                MIN(cms) AS min_cms
            FROM union_price_data
            GROUP BY shop_id
            ORDER BY shop_id
            """
            df_price = bq_client.query(price_final_query).to_dataframe()

        print("BigQuery CMS data retrieved (first few rows):")
        print(df_price.head())
    else:
        df_price = pd.DataFrame(columns=['shop_id','max_cms','avg_cms','min_cms'])

    # 2c. Merge GMV & CMS
    df_gmv['shop_id'] = df_gmv['shop_id'].astype(str)
    df_merged = pd.merge(df_gmv, df_price, on='shop_id', how='outer')

    # ----- Step 3. Build a lookup dictionary (shop_id -> metrics) -----
    metrics_mapping = {}
    for _, row in df_merged.iterrows():
        shop_id_str = str(row['shop_id']).strip()
        metrics_mapping[shop_id_str] = {
            'avg': row.get('avg_gmv'),
            'max': row.get('max_gmv'),
            'min': row.get('min_gmv'),
            'max_cms': row.get('max_cms'),
            'avg_cms': row.get('avg_cms'),
            'min_cms': row.get('min_cms')
        }

    # ----- Step 4. Update Google Sheet -----
    all_values = worksheet.get_all_values()
    total_shops_in_sheet = len(all_values) - 1  # exclude header row

    updates = []
    # Column headers
    if avg_gmv_col_letter:
        updates.append({"range": f"{avg_gmv_col_letter}1", "values": [["AVERAGE GMV"]]})
    if max_gmv_col_letter:
        updates.append({"range": f"{max_gmv_col_letter}1", "values": [["MAX GMV"]]})
    if min_gmv_col_letter:
        updates.append({"range": f"{min_gmv_col_letter}1", "values": [["MIN GMV"]]})

    if max_cms_col_letter:
        updates.append({"range": f"{max_cms_col_letter}1", "values": [["MAX CMS (%)"]]})
    if avg_cms_col_letter:
        updates.append({"range": f"{avg_cms_col_letter}1", "values": [["AVG CMS (%)"]]})
    if min_cms_col_letter:
        updates.append({"range": f"{min_cms_col_letter}1", "values": [["MIN CMS (%)"]]})

    avg_gmv_count = max_gmv_count = min_gmv_count = 0
    max_cms_count = avg_cms_count = min_cms_count_val = 0

    for row_number, row in enumerate(all_values[1:], start=2):
        if len(row) >= shop_id_col_index:
            shop_id_in_sheet = row[shop_id_col_index - 1].strip()
            if shop_id_in_sheet in metrics_mapping:
                data = metrics_mapping[shop_id_in_sheet]

                # GMV columns
                if avg_gmv_col_letter:
                    val = safe_json_value(data['avg'])
                    updates.append({
                        "range": f"{avg_gmv_col_letter}{row_number}",
                        "values": [[val]]
                    })
                    avg_gmv_count += 1

                if max_gmv_col_letter:
                    val = safe_json_value(data['max'])
                    updates.append({
                        "range": f"{max_gmv_col_letter}{row_number}",
                        "values": [[val]]
                    })
                    max_gmv_count += 1

                if min_gmv_col_letter:
                    val = safe_json_value(data['min'])
                    updates.append({
                        "range": f"{min_gmv_col_letter}{row_number}",
                        "values": [[val]]
                    })
                    min_gmv_count += 1

                # CMS columns
                if max_cms_col_letter:
                    raw_cms = safe_json_value(data['max_cms'])
                    final_cms = fix_commission(raw_cms) if raw_cms != "" else ""
                    updates.append({
                        "range": f"{max_cms_col_letter}{row_number}",
                        "values": [[final_cms]]
                    })
                    max_cms_count += 1

                if avg_cms_col_letter:
                    raw_cms = safe_json_value(data['avg_cms'])
                    final_cms = fix_commission(raw_cms) if raw_cms != "" else ""
                    updates.append({
                        "range": f"{avg_cms_col_letter}{row_number}",
                        "values": [[final_cms]]
                    })
                    avg_cms_count += 1

                if min_cms_col_letter:
                    raw_cms = safe_json_value(data['min_cms'])
                    final_cms = fix_commission(raw_cms) if raw_cms != "" else ""
                    updates.append({
                        "range": f"{min_cms_col_letter}{row_number}",
                        "values": [[final_cms]]
                    })
                    min_cms_count_val += 1

    if not updates:
        print("No updates to perform.")
        return

    # Perform batch update
    try:
        worksheet.batch_update(updates)
        print("Batch update successful.")
    except Exception as e:
        print("Error during batch update:", e)
        return

    # ----- Step 5. Format columns in Google Sheet -----
    # GMV columns -> "#,##0 VND"
    if avg_gmv_col_letter:
        worksheet.format(
            f"{avg_gmv_col_letter}:{avg_gmv_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": "#,##0 \"\u20AB\""}}
        )
    if max_gmv_col_letter:
        worksheet.format(
            f"{max_gmv_col_letter}:{max_gmv_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": "#,##0 \"\u20AB\""}}
        )
    if min_gmv_col_letter:
        worksheet.format(
            f"{min_gmv_col_letter}:{min_gmv_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": "#,##0 \"\u20AB\""}}
        )

    # CMS columns -> "0.0%" (always one decimal place)
    # Storing e.g. 0.163 => displays "16.3%"
    cms_format = "0.0%"
    if max_cms_col_letter:
        worksheet.format(
            f"{max_cms_col_letter}:{max_cms_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": cms_format}}
        )
    if avg_cms_col_letter:
        worksheet.format(
            f"{avg_cms_col_letter}:{avg_cms_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": cms_format}}
        )
    if min_cms_col_letter:
        worksheet.format(
            f"{min_cms_col_letter}:{min_cms_col_letter}",
            {"numberFormat": {"type": "NUMBER", "pattern": cms_format}}
        )

    # ----- Step 6. Summary Report -----
    print("\nSummary Report:")
    print(f"Total shops in Google Sheet (excluding header): {total_shops_in_sheet}")
    if avg_gmv_col_letter:
        print(f"Shops updated with AVERAGE GMV: {avg_gmv_count}")
    if max_gmv_col_letter:
        print(f"Shops updated with MAX GMV: {max_gmv_count}")
    if min_gmv_col_letter:
        print(f"Shops updated with MIN GMV: {min_gmv_count}")
    if max_cms_col_letter:
        print(f"Shops updated with MAX CMS: {max_cms_count}")
    if avg_cms_col_letter:
        print(f"Shops updated with AVG CMS: {avg_cms_count}")
    if min_cms_col_letter:
        print(f"Shops updated with MIN CMS: {min_cms_count_val}")

    overall_updates = max(
        avg_gmv_count, max_gmv_count, min_gmv_count,
        max_cms_count, avg_cms_count, min_cms_count_val
    )
    not_found = total_shops_in_sheet - overall_updates
    print(f"Shops without historical data: {not_found}")

if __name__ == "__main__":
    main()
