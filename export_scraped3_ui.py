import sys
from PyQt6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                           QFormLayout, QLabel, QLineEdit, QPushButton, 
                           QComboBox, QMessageBox, QTextEdit, QButtonGroup, 
                           QRadioButton, QTabWidget, QScrollArea, QSizePolicy)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import pandas as pd
from datetime import datetime, timedelta
import logging
import unicodedata  # Thêm cho safe_encode
from gsheet_manager import GSheetManager
import export_scraped3 as scraped3_logic

# Hàm hỗ trợ xử lý lỗi encoding tiếng Việt
def safe_encode(text, default=''):
    """Xử lý vấn đề encoding với tiếng Việt, trả về text an toàn hoặc giá trị mặc định"""
    if text is None:
        return default
        
    if isinstance(text, str):
        try:
            # Thử chuẩn hóa Unicode
            return unicodedata.normalize('NFKD', text)
        except:
            try:
                # <PERSON><PERSON><PERSON> không thành công, thử lọc ký tự không ASCII
                return ''.join(c for c in text if ord(c) < 128)
            except:
                return default
    return str(text)

class ExportWorker(QThread):
    """Worker thread for export operations"""
    progress = pyqtSignal(str)  # Progress message
    finished = pyqtSignal(bool, str)  # Success, error message
    
    def __init__(self, mode_func, args):
        super().__init__()
        self.mode_func = mode_func
        self.args = args
        self.gsheet_manager = None

    def run(self):
        try:
            # Initialize GSheetManager
            self.gsheet_manager = GSheetManager()
            
            # Open sheet
            sheet, error = self.gsheet_manager.open_sheet(self.args['sheet_url'])
            if error:
                raise ValueError(error)
                
            ws = sheet.worksheet(self.args['worksheet_name'])
            
            # Báo cáo tiến trình
            self.progress.emit(f"Đã kết nối đến worksheet: {self.args['worksheet_name']}")
            
            # Chuẩn bị các tham số từ UI để truyền vào hàm core logic
            if 'shop_mode' in self.args and self.args['shop_mode']:
                # Shop level mode
                self.progress.emit(f"Thực hiện export Shop-level mode")
                self.mode_func(
                    self.gsheet_manager, 
                    sheet, 
                    ws,
                    shop_col_letter=self.args.get('shop_col'),
                    shop_l30d_col_letter=self.args.get('l30d_col'),
                    shop_wishlist_col_letter=self.args.get('wishlist_col'),
                    date_str=self.args.get('date')
                )
            else:
                # Item level mode
                self.progress.emit(f"Thực hiện export Item-level mode") 
                self.mode_func(
                    self.gsheet_manager,
                    sheet,
                    ws,
                    item_col_letter=self.args.get('item_col'),
                    shop_col_letter=self.args.get('shop_col'),
                    start_scraped_col_letter=self.args.get('start_col'),
                    link_col_letter=self.args.get('link_col'),
                    date_str=self.args.get('date')
                )
            
            self.progress.emit("Xử lý hoàn tất")
            self.finished.emit(True, "")
            
        except Exception as e:
            self.finished.emit(False, safe_encode(str(e), "Lỗi không xác định"))

class ExportScraped3Widget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Export Scraped Data UI") 
        self.setMinimumWidth(600)
        
        # Initialize GSheetManager
        self.gsheet_manager = GSheetManager()
        self.export_worker = None
        self.loading_dialog = None
        self.is_loading = False
        
        self.setupUI()
        self.connectSignals()

    def setupUI(self):
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Sheet Info Group
        sheet_group = QGroupBox("Sheet Info")
        sheet_layout = QVBoxLayout()
        
        # URL and Load button
        url_layout = QHBoxLayout()
        self.sheet_url_edit = QLineEdit()
        self.load_sheet_btn = QPushButton("Load")
        self.load_sheet_btn.setMaximumWidth(60)
        url_layout.addWidget(self.sheet_url_edit)
        url_layout.addWidget(self.load_sheet_btn)
        
        self.worksheet_combo = QComboBox()
        
        sheet_layout.addLayout(url_layout)
        sheet_layout.addWidget(QLabel("Select Worksheet:"))
        sheet_layout.addWidget(self.worksheet_combo)
        sheet_group.setLayout(sheet_layout)
        
        # Export Mode Group
        mode_group = QGroupBox("Export Mode")
        mode_layout = QVBoxLayout()
        self.mode_shop = QRadioButton("Shop-level (sum L30d GMV + top 3 items)")
        self.mode_item = QRadioButton("Item-level (max L30d GMV variation)")
        self.mode_shop.setChecked(True)
        mode_layout.addWidget(self.mode_shop)
        mode_layout.addWidget(self.mode_item)
        mode_group.setLayout(mode_layout)
        
        # Basic Settings Group
        basic_group = QGroupBox("Column Settings") 
        basic_layout = QFormLayout()
        
        self.col_shop = QLineEdit()
        self.col_item = QLineEdit()
        basic_layout.addRow("Cột Shop ID:", self.col_shop)
        basic_layout.addRow("Cột Item ID:", self.col_item)
        
        # Shop-level output columns
        self.shop_l30d_col = QLineEdit()
        self.shop_wishlist_col = QLineEdit()
        basic_layout.addRow("Cột L30D GMV:", self.shop_l30d_col)
        basic_layout.addRow("Cột Wishlist Item:", self.shop_wishlist_col)
        
        # Item-level output columns
        self.item_start_col = QLineEdit()
        self.item_link_col = QLineEdit()
        basic_layout.addRow("Cột 30D GMV:", self.item_start_col)
        basic_layout.addRow("Cột Link hình:", self.item_link_col)
        
        # Date input
        self.date_input = QLineEdit()
        self.date_input.setPlaceholderText("YYYY-MM-DD")
        basic_layout.addRow("Ngày lấy dữ liệu:", self.date_input)
        
        basic_group.setLayout(basic_layout)
        
        # Run button
        self.run_button = QPushButton("Start Export")
        self.run_button.setMinimumHeight(35)
        self.run_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # Log area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(100)
        
        # Add all components to main layout
        main_layout.addWidget(sheet_group)
        main_layout.addWidget(mode_group)
        main_layout.addWidget(basic_group)
        main_layout.addStretch()
        main_layout.addWidget(self.run_button)
        main_layout.addWidget(self.log_text)

        # Initialize mode-specific fields (call after all UI elements are created)
        self.initializeModeFields()

    def connectSignals(self):
        self.load_sheet_btn.clicked.connect(self.loadSheets)
        self.run_button.clicked.connect(self.runExport)
        self.worksheet_combo.currentIndexChanged.connect(self.onWorksheetSelected)
        self.mode_shop.toggled.connect(self.onModeChanged)
        self.mode_item.toggled.connect(self.onModeChanged)
        
    def initializeModeFields(self):
        """Initialize field states based on default mode (Shop)"""
        # Disable Item ID field and Item mode fields initially
        self.col_item.setEnabled(False)
        self.item_start_col.setEnabled(False)
        self.item_link_col.setEnabled(False)
        
        # Enable Shop mode fields
        self.shop_l30d_col.setEnabled(True)
        self.shop_wishlist_col.setEnabled(True)

    def log(self, message):
        """Add message to log with timestamp"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            # Sử dụng safe_encode để đảm bảo không có lỗi encoding
            safe_message = safe_encode(message, "Lỗi hiển thị thông báo")
            self.log_text.append(f"[{timestamp}] {safe_message}")
            self.log_text.verticalScrollBar().setValue(
                self.log_text.verticalScrollBar().maximum()
            )
        except Exception:
            # Nếu có lỗi khi log, không làm gì để tránh crash
            pass

    def loadSheets(self):
        """Load sheets and update UI"""
        try:
            if self.is_loading:
                return
                
            sheet_url = self.sheet_url_edit.text().strip()
            if not sheet_url:
                raise ValueError("Vui lòng nhập URL Google Sheet")
                
            self.is_loading = True
            self.load_sheet_btn.setEnabled(False)
            self.run_button.setEnabled(False)
            
            self.log("Đang kết nối Google Sheet...")
            sheet, error = self.gsheet_manager.open_sheet(sheet_url)
            if error:
                raise ValueError(error)
                
            self.worksheet_combo.clear()
            for ws in sheet.worksheets():
                self.worksheet_combo.addItem(ws.title)
                
            self.log("Đã tải danh sách worksheet thành công")
            
            # Re-apply mode settings after loading
            self.onModeChanged()
            
        except Exception as e:
            self.log(f"Lỗi khi tải worksheet: {safe_encode(str(e), 'Lỗi không xác định')}")
            QMessageBox.warning(self, "Lỗi", safe_encode(str(e), "Lỗi không xác định"))
            
        finally:
            self.is_loading = False
            self.load_sheet_btn.setEnabled(True)
            self.run_button.setEnabled(True)

    def onWorksheetSelected(self, index):
        """Handle worksheet selection changed"""
        # Empty function since we don't need auto-detection anymore
        pass

    def loadWorksheetData(self, worksheet):
        """Empty function since we don't need auto-detection anymore"""
        pass

    def detectColumns(self, headers=None):
        """Empty function since we don't need auto-detection anymore"""
        pass

    def onModeChanged(self):
        """Handle mode radio button changes"""
        is_shop_mode = self.mode_shop.isChecked()
        
        # Item ID field
        self.col_item.setEnabled(not is_shop_mode)
        
        # Shop mode fields
        self.shop_l30d_col.setEnabled(is_shop_mode)
        self.shop_wishlist_col.setEnabled(is_shop_mode)
        
        # Item mode fields
        self.item_start_col.setEnabled(not is_shop_mode)
        self.item_link_col.setEnabled(not is_shop_mode)

    def getExportArgs(self):
        args = {
            'sheet_url': self.sheet_url_edit.text().strip(),
            'worksheet_name': self.worksheet_combo.currentText(),
            'shop_col': self.col_shop.text().strip(),
            'item_col': self.col_item.text().strip(),
            'date': self.date_input.text().strip(),
            'shop_mode': self.mode_shop.isChecked()
        }
        
        # Add mode-specific args
        if self.mode_shop.isChecked():
            args.update({
                'l30d_col': self.shop_l30d_col.text().strip(),
                'wishlist_col': self.shop_wishlist_col.text().strip()
            })
        else:
            args.update({
                'start_col': self.item_start_col.text().strip(),
                'link_col': self.item_link_col.text().strip() 
            })
            
        return args

    def runExport(self):
        try:
            self.run_button.setEnabled(False)
            args = self.getExportArgs()
            
            # Select mode function
            mode_func = scraped3_logic.run_shop_level_mode if self.mode_shop.isChecked() else scraped3_logic.run_item_level_mode
            
            # Create and start worker
            self.export_worker = ExportWorker(mode_func, args)
            self.export_worker.progress.connect(self.log)
            self.export_worker.finished.connect(self.onExportFinished)
            self.export_worker.start()
            
        except Exception as e:
            self.log(f"Lỗi khi bắt đầu export: {safe_encode(str(e), 'Lỗi không xác định')}")
            self.run_button.setEnabled(True)

    def onExportFinished(self, success, error_msg):
        self.run_button.setEnabled(True)
        if success:
            self.log("Export đã hoàn tất thành công!")
        else:
            self.log(f"Export thất bại: {safe_encode(error_msg, 'Lỗi không xác định')}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ExportScraped3Widget()
    window.show()
    sys.exit(app.exec())
