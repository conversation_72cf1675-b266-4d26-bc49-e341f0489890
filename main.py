# -*- coding: utf-8 -*-
import sys
import os
import io

# Configure UTF-8 encoding for stdout/stderr to handle Vietnamese characters
if hasattr(sys, 'frozen'):  # Running as compiled .exe
    import codecs
    try:
        if sys.stdout is not None and hasattr(sys.stdout, 'buffer'):
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        if sys.stderr is not None and hasattr(sys.stderr, 'buffer'):
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except (AttributeError, OSError):
        # If encoding setup fails in .exe, continue without it
        pass
else:
    # For development environment
    try:
        if hasattr(sys.stdout, 'buffer') and not hasattr(sys.stdout, '_wrapped'):
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
            sys.stdout._wrapped = True
        if hasattr(sys.stderr, 'buffer') and not hasattr(sys.stderr, '_wrapped'):
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
            sys.stderr._wrapped = True
    except (AttributeError, OSError):
        # If already wrapped or other issues, skip
        pass

import subprocess
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QPushButton, QLabel, QStackedWidget, QMessageBox,
                            QFileDialog, QDialog, QProgressBar)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QIcon  # Added import for window icon
import export_shop3
import export_shop3_ui
import export_item6
import export_item6_ui
import export_scraped3
import export_scraped3_ui
from gsheet_manager import GSheetManager
from pathlib import Path

# Thêm những dòng này ở đầu file
os.environ['GRPC_ENABLE_FORK_SUPPORT'] = '0'
os.environ['GRPC_DNS_RESOLVER'] = 'native'
os.environ['GOOGLE_CLOUD_PROJECT'] = 'beyondk-live-data'

class HomeWidget(QWidget):
    def __init__(self, stacked):
        super().__init__()
        self.stacked = stacked
        layout = QVBoxLayout()
        
        # Add spacer at top to push content to center
        layout.addStretch()
        
        # Container for buttons
        buttons_container = QWidget()
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setSpacing(20)  # Space between buttons
        
        # Style buttons
        btn_style = """
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                padding: 15px 32px;
                font-size: 14px;
                min-width: 200px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
        """
        
        btn_shop2 = QPushButton("Export Shop")
        btn_item4 = QPushButton("Export Item") 
        btn_scraped3 = QPushButton("Export Scraped")  # Add new button
        
        # Apply style to buttons
        btn_shop2.setStyleSheet(btn_style)
        btn_item4.setStyleSheet(btn_style)
        btn_scraped3.setStyleSheet(btn_style)
        
        # Add buttons to layout
        buttons_layout.addWidget(btn_shop2, alignment=Qt.AlignmentFlag.AlignCenter)
        buttons_layout.addWidget(btn_item4, alignment=Qt.AlignmentFlag.AlignCenter)
        buttons_layout.addWidget(btn_scraped3, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # Add buttons container to main layout
        layout.addWidget(buttons_container, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # Add spacer at bottom to push content to center
        layout.addStretch()
        
        # Connect buttons
        btn_shop2.clicked.connect(lambda: self.stacked.setCurrentIndex(1))
        btn_item4.clicked.connect(lambda: self.stacked.setCurrentIndex(2))
        btn_scraped3.clicked.connect(lambda: self.stacked.setCurrentIndex(3))  # New index
        
        self.setLayout(layout)

class Shop2Widget(QWidget):
    def __init__(self, stacked):
        super().__init__()
        self.stacked = stacked
        layout = QVBoxLayout()
        
        # Create and add ExportShop3Widget from v2
        self.export_widget = export_shop3_ui.ExportShop3Widget()
        layout.addWidget(self.export_widget)
        
        # Add back button
        btn_back = QPushButton("Back to Mainscreen")
        btn_back.clicked.connect(lambda: self.stacked.setCurrentIndex(0))
        layout.addWidget(btn_back)
        
        self.setLayout(layout)

    def showEvent(self, event):
        super().showEvent(event)
        # Nothing special needed here anymore since the widget is already visible
        pass

class Item6Widget(QWidget):
    def __init__(self, stacked):
        super().__init__()
        self.stacked = stacked
        layout = QVBoxLayout()
        
        # Create and add ExportItem4Widget
        self.export_widget = export_item6_ui.ExportItem6Widget()
        layout.addWidget(self.export_widget)
        
        # Add back button
        btn_back = QPushButton("Back to Mainscreen")
        btn_back.clicked.connect(lambda: self.stacked.setCurrentIndex(0))
        layout.addWidget(btn_back)
        
        self.setLayout(layout)

class Scraped3Widget(QWidget):
    def __init__(self, stacked):
        super().__init__()
        self.stacked = stacked
        layout = QVBoxLayout()
        
        # Create and add ExportScraped3Widget
        self.export_widget = export_scraped3_ui.ExportScraped3Widget()
        layout.addWidget(self.export_widget)
        
        # Add back button
        btn_back = QPushButton("Back to Mainscreen")
        btn_back.clicked.connect(lambda: self.stacked.setCurrentIndex(0))
        layout.addWidget(btn_back)
        
        self.setLayout(layout)

class LoadingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Connecting to Google Services")
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowCloseButtonHint)
        self.setModal(True)
        self.setFixedSize(300, 100)
        
        layout = QVBoxLayout(self)
        
        # Status label
        self.status_label = QLabel("Initializing connection...")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress = QProgressBar()
        self.progress.setRange(0, 0)  # Indeterminate progress
        layout.addWidget(self.progress)
        
        # Center on screen
        screen = QApplication.primaryScreen().geometry()
        self.move(
            (screen.width() - self.width()) // 2,
            (screen.height() - self.height()) // 2
        )

    def update_status(self, message):
        self.status_label.setText(message)

class GoogleAuthWorker(QThread):
    finished = pyqtSignal(bool, str, dict)  # Add permissions dict to signal
    status_update = pyqtSignal(str)

    def __init__(self, gsheet_manager, credentials_path):
        super().__init__()
        self.gsheet_manager = gsheet_manager
        self.credentials_path = credentials_path

    def run(self):
        try:
            self.status_update.emit("Đang xác thực...")
            if not self.gsheet_manager.authenticate():
                self.finished.emit(False, "Xác thực thất bại", {})
                return
                
            self.status_update.emit("Kiểm tra quyền truy cập API...")
            # Get detailed permissions status
            permissions = self.gsheet_manager.test_permissions()
            
            if all(permissions.values()):
                self.status_update.emit("Xác thực thành công!")
                self.finished.emit(True, "", permissions)
            else:
                missing = [api for api, has_access in permissions.items() if not has_access]
                error_msg = "Thiếu quyền truy cập cho:\n"
                permission_names = {
                    'sheets': 'Google Sheets API',
                    'drive': 'Google Drive API', 
                    'bigquery': 'BigQuery API'
                }
                error_msg += "\n".join(f"- {permission_names[p]}" for p in missing)
                self.finished.emit(False, error_msg, permissions)
                
        except Exception as e:
            self.finished.emit(False, str(e), {})

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # Fix icon loading by using absolute path
        icon_path = os.path.join(os.path.dirname(__file__), 'UI.ico')
        if os.path.exists(icon_path):
            # Set window icon
            app_icon = QIcon(icon_path)
            self.setWindowIcon(app_icon)
            # Also set application-wide icon (for taskbar)
            QApplication.setWindowIcon(app_icon)

        self.setWindowTitle("Big Query Export")
        self.setFixedSize(700, 700)

        # Initialize GSheetManager first
        self.gsheet_manager = GSheetManager()
        
        # Setup UI but don't show window yet
        self.stacked = QStackedWidget()
        self.setCentralWidget(self.stacked)
        home = HomeWidget(self.stacked)
        shop2 = Shop2Widget(self.stacked)
        item6 = Item6Widget(self.stacked)
        scraped3 = Scraped3Widget(self.stacked)  # Add new widget
        self.stacked.addWidget(home)     # index 0
        self.stacked.addWidget(shop2)    # index 1
        self.stacked.addWidget(item6)    # index 2
        self.stacked.addWidget(scraped3) # index 3
        
        # Center window on screen
        self.centerWindow()
        
        # Store initial permissions state
        self.has_all_permissions = False
        
        # Setup permission check timer (check every 3 minutes)
        self.permission_timer = QTimer()
        self.permission_timer.timeout.connect(self.check_permissions)
        self.permission_timer.start(180000)
        
        self.auth_completed = False  # Add flag to track auth completion

    def check_permissions(self):
        """Periodically check if missing permissions have been granted"""
        if not self.has_all_permissions:
            if self.gsheet_manager.recheck_authentication():
                self.has_all_permissions = True
                QMessageBox.information(
                    self,
                    "Permissions Updated",
                    "All required permissions are now available!"
                )

    def init_google_services(self) -> None:
        """Initialize Google services without blocking the UI"""
        try:
            if self.gsheet_manager.credentials_exist():
                self.loading_dialog = LoadingDialog(self)
                self.loading_dialog.show()
                self.loading_dialog.update_status("Kiểm tra xác thực hiện có...")

                # Thay vì authenticate đồng bộ, ta chạy bằng worker:
                self.auth_worker = GoogleAuthWorker(self.gsheet_manager, self.gsheet_manager._credentials_path)
                self.auth_worker.status_update.connect(self.loading_dialog.update_status)
                self.auth_worker.finished.connect(
                    lambda success, error, perms: self.handle_auth_result(success, error, perms, self.loading_dialog)
                )
                self.auth_worker.start()
            else:
                # Nếu credentials chưa tồn tại, yêu cầu người dùng chọn file
                credentials_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "Chọn file credentials.json",
                    "",
                    "JSON Files (*.json);;All Files (*)"
                )
                if not credentials_path:
                    QMessageBox.critical(self, "Lỗi", "Không tìm thấy file credentials.json.")
                    sys.exit(1)
                
                self.loading_dialog = LoadingDialog(self)
                self.loading_dialog.show()
                self.loading_dialog.update_status("Đang sao chép file credentials...")
                
                if not self.gsheet_manager.set_credentials_path(credentials_path):
                    self.loading_dialog.close()
                    QMessageBox.critical(self, "Lỗi", "Không thể sao chép file credentials.")
                    sys.exit(1)

                # Tạo worker xác thực
                self.auth_worker = GoogleAuthWorker(self.gsheet_manager, self.gsheet_manager._credentials_path)
                self.auth_worker.status_update.connect(self.loading_dialog.update_status)
                self.auth_worker.finished.connect(
                    lambda success, error, perms: self.handle_auth_result(success, error, perms, self.loading_dialog)
                )
                self.auth_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "Lỗi khởi tạo", f"Lỗi khởi tạo Google services: {str(e)}")
            sys.exit(1)

    def handle_auth_result(self, success: bool, error_msg: str, perms: dict, dialog: LoadingDialog):
        """Handle worker result without blocking UI"""
        dialog.close()
        
        if success:
            self.has_all_permissions = True
            self.auth_completed = True
            # Hiển thị cửa sổ chính sau khi xác thực xong
            self.show()
        else:
            warning = QMessageBox.warning(
                self,
                "Missing Permissions",
                error_msg + "\n\nDo you want to continue anyway?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if warning == QMessageBox.StandardButton.Yes:
                self.has_all_permissions = False
                self.auth_completed = True
                self.show()
            else:
                sys.exit(1)

    def centerWindow(self):
        """Center the main window on screen with vertical offset"""
        # Get the screen geometry
        screen = QApplication.primaryScreen().geometry()
        # Get window geometry
        size = self.geometry()
        # Calculate center position with 100px offset upward
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2 - 40  # Subtract 100px to move up
        # Move window to center
        self.move(x, y)

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Set high DPI scaling
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    window = MainWindow()

    # Thay vì kiểm tra kết quả trả về, hãy khởi tạo rồi cho chạy app
    window.init_google_services()
    sys.exit(app.exec())
