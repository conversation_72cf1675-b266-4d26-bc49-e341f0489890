import sys
import gspread
from google.cloud import bigquery
import pandas as pd
import re
import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from collections import defaultdict  # Import here only ONCE
import io
import unicodedata

# Set UTF-8 encoding for stdout and stderr to handle Vietnamese characters
if sys.platform.startswith('win'):
    # For Windows, ensure UTF-8 encoding
    try:
        if hasattr(sys.stdout, 'buffer') and not hasattr(sys.stdout, '_wrapped'):
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
            sys.stdout._wrapped = True
        if hasattr(sys.stderr, 'buffer') and not hasattr(sys.stderr, '_wrapped'):
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
            sys.stderr._wrapped = True
    except (AttributeError, OSError):
        # If already wrapped or other issues, skip
        pass

# Configure logging with UTF-8 encoding support
class UTF8StreamHandler(logging.StreamHandler):
    def emit(self, record):
        try:
            # Check if stream is available (important for .exe builds)
            if self.stream is None:
                return  # Skip logging if no stream available

            msg = self.format(record)
            # Ensure message is properly encoded
            if isinstance(msg, str):
                # Normalize Unicode characters
                msg = unicodedata.normalize('NFKD', msg)
            stream = self.stream
            stream.write(msg + self.terminator)
            stream.flush()
        except (UnicodeEncodeError, UnicodeDecodeError):
            # Fallback: remove non-ASCII characters
            try:
                if self.stream is None:
                    return  # Skip if no stream
                msg = self.format(record)
                msg = ''.join(c for c in msg if ord(c) < 128)
                stream = self.stream
                stream.write(msg + self.terminator)
                stream.flush()
            except:
                # Last resort: skip the problematic log message
                pass
        except (AttributeError, OSError):
            # Handle cases where stream is None or unavailable (common in .exe builds)
            pass

# Configure logging with UTF-8 handler - safe for .exe builds
def setup_safe_logging():
    """Setup logging that works in both development and .exe environments"""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Remove default handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Try to add UTF-8 handler, fallback to NullHandler if needed
    try:
        utf8_handler = UTF8StreamHandler()
        utf8_handler.setFormatter(logging.Formatter("%(levelname)s: %(message)s"))
        logger.addHandler(utf8_handler)
    except (AttributeError, OSError):
        # Fallback for .exe builds where streams might not be available
        null_handler = logging.NullHandler()
        logger.addHandler(null_handler)

    return logger

# Initialize logger
logger = setup_safe_logging()


def safe_encode_text(text, default=''):
    """
    Safely encode text to handle Vietnamese characters and avoid encoding errors
    """
    if text is None:
        return default

    if isinstance(text, str):
        try:
            # First try to normalize Unicode
            normalized = unicodedata.normalize('NFKD', text)
            # Test if it can be encoded to the system's default encoding
            normalized.encode(sys.stdout.encoding or 'utf-8', errors='strict')
            return normalized
        except (UnicodeEncodeError, UnicodeDecodeError, AttributeError):
            try:
                # If normalization fails, try to remove problematic characters
                return ''.join(c for c in text if ord(c) < 128)
            except:
                return default
    return str(text)


def safe_log(message):
    """
    Safely log messages with Vietnamese characters - safe for .exe builds
    """
    try:
        safe_message = safe_encode_text(message, "Log message encoding error")
        logger.info(safe_message)
    except (AttributeError, OSError):
        # If logging fails (common in .exe builds), silently continue
        pass


def col_letter_to_index(letter):
    """
    Convert an Excel-style column letter (e.g., 'A', 'B', 'AA') to a 1-indexed column number.
    """
    letter = letter.upper().strip()
    index = 0
    for char in letter:
        index = index * 26 + (ord(char) - ord('A') + 1)
    return index


def format_number(value):
    """
    Format a number with thousands separators (#,##0)
    """
    try:
        num = float(value)
        if num == int(num):  # If it's a whole number
            return f"{int(num):,}"
        return f"{num:,.2f}"  # Format with 2 decimal places and commas
    except (ValueError, TypeError):
        return value  # Return the original value if it can't be converted


def build_filter(shop_item_pairs):
    """
    Given a list of (shop_id, item_id) pairs, build a BigQuery WHERE clause string.
    """
    grouped = defaultdict(list)
    for (shop_id, item_id) in shop_item_pairs:
        grouped[shop_id].append(item_id)
    clauses = []
    for shop_id, item_ids in grouped.items():
        item_ids = list(set(item_ids))
        item_ids_str = ", ".join(str(i) for i in item_ids)
        clauses.append(f"(s.shop_id = {shop_id} AND s.item_id IN ({item_ids_str}))")
    return " OR ".join(clauses)


def parse_sales_table_date(table_name):
    """
    Parse the date from a Sales_data (or price_data) table name in the format: YYYY_MM_DD_KOLname
    Returns a datetime object or None if parsing fails.
    """
    parts = table_name.split("_")
    if len(parts) < 4:
        return None
    yyyy, mm, dd = parts[0], parts[1], parts[2]
    try:
        return datetime(year=int(yyyy), month=int(mm), day=int(dd))
    except ValueError:
        return None


def parse_price_campaign_info(table_name):
    """
    Given a price_data table name of the form YYYY_MM_DD_KOLname,
    return a string with:
      first line: DD_MM_YY (last two digits of year)
      second line: KOL name
    """
    parts = table_name.split("_")
    if len(parts) >= 4:
        yyyy, mm, dd, kol = parts[0], parts[1], parts[2], parts[3]
        date_str = f"{dd}_{mm}_{yyyy[-2:]}"
        return f"{date_str}\n{kol}"
    else:
        return "N/A"


def filter_tables_for_completed_months(table_names, months_count):
    """
    Returns the subset of table_names whose campaign date is within the last
    `months_count` months, INCLUSIVE of the current month.

    Example: If running in April 2025:
      - Current month is April 2025
      - We include the partial current month (April)
      - We include previous months going back as requested
    
    If months_count=3 => includes April 2025, March 2025, Feb 2025, Jan 2025
    If months_count=6 => includes April 2025, March 2025, Feb 2025, Jan 2025, Dec 2024, Nov 2024, Oct 2024
    """
    today = datetime.today()
    # Last day of the range is today
    end_of_range = today
    # Start of the range is (months_count - 1) months ago from the 1st of current month
    start_of_current_month = datetime(today.year, today.month, 1)
    start_of_range_temp = start_of_current_month - relativedelta(months=months_count - 1)
    # Snap to the 1st day of that month
    start_of_range = datetime(start_of_range_temp.year, start_of_range_temp.month, 1)

    results = []
    for name in table_names:
        d = parse_sales_table_date(name)
        if d is not None and (start_of_range <= d <= end_of_range):
            results.append(name)
    return results


class PriceAPI:
    def get_price_table_list(self, bq_client=None):
        """
        Retrieve list of price_data tables.
        """
        if bq_client is None:
            # Tạo client mới nếu không được cung cấp (để tương thích với code cũ)
            bq_client = bigquery.Client(project='beyondk-live-data')
            safe_log("Created new BigQuery client for price_table_list")

        price_tbl_q = "SELECT table_name FROM `beyondk-live-data.price_data.INFORMATION_SCHEMA.TABLES`"
        try:
            df = bq_client.query(price_tbl_q).to_dataframe()
            if df.empty:
                safe_log("No price tables found in query result (empty dataframe)")
                return []
            return df["table_name"].tolist()
        except Exception as e:
            safe_log(f"Error retrieving price_data table list: {e}")
            return []

    def get_chunked_price_data(self, bq_client, combined_condition, table_name, detect_nan=False):
        """
        Execute a price query on one table for a given combined condition.
        Returns a DataFrame with an extra 'table' column set to table_name.

        If detect_nan=True, sẽ trả về thêm danh sách các hàng có giá trị NaN.
        Returns: tuple (result_df, nan_rows) where both are pandas DataFrames
        """
        import gc
        import time

        MAX_RETRIES = 2  # Giảm retry để tăng tốc độ
        RETRY_DELAY = 0.5  # Giảm delay

        # Nếu không cần phát hiện NaN, sử dụng truy vấn đã được lọc trước
        if not detect_nan:
            # Truy vấn hiệu quả - đã loại bỏ NULL tại server
            query_str = f"""
            SELECT shop_id, origin_price, MIN(final_price) as lowest_price
            FROM `beyondk-live-data.price_data.{table_name}`
            WHERE {combined_condition}
            GROUP BY shop_id, origin_price
            HAVING MIN(final_price) IS NOT NULL
            """

            # Retry mechanism cho price query
            for retry in range(MAX_RETRIES):
                try:
                    # Thêm timeout và caching cho query
                    job_config = bigquery.QueryJobConfig()
                    job_config.job_timeout_ms = 120000  # Giảm xuống 2 phút
                    job_config.use_query_cache = True   # Enable caching

                    result_df = bq_client.query(query_str, job_config=job_config).to_dataframe()
                    if not result_df.empty:
                        result_df['table'] = table_name
                        safe_log(f"Found {len(result_df)} valid price entries in table {table_name}")

                    # Trả về kết quả và DataFrame NaN rỗng
                    empty_nan = pd.DataFrame(columns=['shop_id', 'origin_price', 'item_id', 'pitching_item_id', 'final_price', 'table_name'])
                    gc.collect()  # Giải phóng memory
                    return result_df, empty_nan

                except Exception as ex:
                    if retry < MAX_RETRIES - 1:
                        safe_log(f"Error querying price table {table_name} (attempt {retry + 1}/{MAX_RETRIES}): {ex}")
                        time.sleep(RETRY_DELAY)
                    else:
                        safe_log(f"Failed to query price table {table_name} after {MAX_RETRIES} attempts: {ex}")
                        empty_result = pd.DataFrame(columns=['shop_id', 'origin_price', 'lowest_price', 'table'])
                        empty_nan = pd.DataFrame(columns=['shop_id', 'origin_price', 'item_id', 'pitching_item_id', 'final_price', 'table_name'])
                        return empty_result, empty_nan

        # Chế độ phát hiện NaN - truy vấn đầy đủ với retry
        query_str = f"""
        SELECT shop_id, origin_price, item_id, pitching_item_id, final_price
        FROM `beyondk-live-data.price_data.{table_name}`
        WHERE {combined_condition}
        """

        for retry in range(MAX_RETRIES):
            try:
                # Thêm timeout cho query
                job_config = bigquery.QueryJobConfig()
                job_config.job_timeout_ms = 300000  # 5 minutes timeout

                # Lấy tất cả dữ liệu không lọc NULL
                raw_df = bq_client.query(query_str, job_config=job_config).to_dataframe()
                # Khởi tạo DataFrame trống cho các hàng NaN
                nan_rows = pd.DataFrame(columns=['shop_id', 'origin_price', 'item_id', 'pitching_item_id', 'final_price', 'table_name'])

                if not raw_df.empty:
                    # Chuyển đổi các cột số về định dạng thích hợp
                    raw_df['final_price'] = pd.to_numeric(raw_df['final_price'], errors='coerce')
                    raw_df['origin_price'] = pd.to_numeric(raw_df['origin_price'], errors='coerce')
                    raw_df['shop_id'] = pd.to_numeric(raw_df['shop_id'], errors='coerce')

                    # Phát hiện và ghi lại các hàng có giá trị NaN trong final_price
                    nan_mask = raw_df['final_price'].isna()
                    if nan_mask.any():
                        nan_rows = raw_df[nan_mask].copy()
                        nan_rows['table_name'] = table_name
                        safe_log(f"Found {len(nan_rows)} rows with NaN final_price in {table_name}")

                        # In ra 5 mẫu đầu tiên để debug
                        if len(nan_rows) > 0:
                            sample_nan = nan_rows.head(5).to_dict('records')
                            safe_log(f"Sample NaN rows in {table_name}: {sample_nan}")

                    # Tiếp tục xử lý dữ liệu bình thường - Nhóm theo và tính MIN
                    result_df = pd.DataFrame(columns=['shop_id', 'origin_price', 'lowest_price', 'table'])
                    # Tạo DataFrame kết quả đã tổng hợp bằng cách nhóm và tính MIN
                    valid_df = raw_df.dropna(subset=['final_price'])
                    if not valid_df.empty:
                        result_df = valid_df.groupby(['shop_id', 'origin_price'], as_index=False).agg({
                            'final_price': 'min'
                        }).rename(columns={'final_price': 'lowest_price'})

                        # Thêm cột table name
                        if not result_df.empty:
                            result_df['table'] = table_name
                            safe_log(f"Found {len(result_df)} valid price entries in table {table_name}")
                        else:
                            safe_log(f"No valid price data in table {table_name} after filtering")

                    # Giải phóng memory
                    del raw_df, valid_df
                    gc.collect()

                # Trả về kết quả và danh sách các hàng NaN - luôn là DataFrame
                return result_df, nan_rows

            except Exception as ex:
                if retry < MAX_RETRIES - 1:
                    safe_log(f"Error querying price table {table_name} (attempt {retry + 1}/{MAX_RETRIES}): {ex}")
                    time.sleep(RETRY_DELAY)
                else:
                    safe_log(f"Failed to query price table {table_name} after {MAX_RETRIES} attempts: {ex}")
                    # Trả về hai DataFrames trống để đảm bảo tính nhất quán
                    empty_result = pd.DataFrame(columns=['shop_id', 'origin_price', 'lowest_price', 'table'])
                    empty_nan = pd.DataFrame(columns=['shop_id', 'origin_price', 'item_id', 'pitching_item_id', 'final_price', 'table_name'])
                    return empty_result, empty_nan


def query_gmv_for_tables(bq_client, variation_map, table_list):
    """
    Given a list of Sales_data tables and a variation map (shop_id, cpid) -> [item_ids],
    query each table for sum of GMV in chunked manner.
    Returns a DataFrame with columns:
        [shop_id, item_id, table_gmv, table_name, kol_name, campaign_date]
    If no data is found, returns an empty DataFrame.
    """
    import gc
    import time

    # Tối ưu chunk size để tăng tốc độ tối đa
    CHUNK_SIZE = 500  # Tăng lên để giảm số chunk xuống còn 6
    MAX_RETRIES = 2   # Giảm số retry để tăng tốc độ
    RETRY_DELAY = 0.5 # Giảm delay xuống 0.5s
    partial_results = []

    def parse_table_info(tbl_name):
        parts = tbl_name.split("_")
        if len(parts) >= 4:
            yyyy, mm, dd, kol = parts[0], parts[1], parts[2], parts[3]
            camp_date = f"{dd}_{mm}_{yyyy[-2:]}"
            return kol, camp_date
        else:
            return "Unknown", "N/A"

    variation_items = list(variation_map.items())  # [((shop, cpid), [item_ids]), ...]
    total_chunks = (len(variation_items) + CHUNK_SIZE - 1) // CHUNK_SIZE

    safe_log(f"Processing {len(variation_items)} variations in {total_chunks} chunks of max {CHUNK_SIZE} items each")

    for chunk_idx in range(0, len(variation_items), CHUNK_SIZE):
        chunk = variation_items[chunk_idx : chunk_idx + CHUNK_SIZE]
        chunk_pairs = []
        for (sid, _cpid), item_list in chunk:
            for iid in set(item_list):
                chunk_pairs.append((sid, iid))

        # Tăng threshold để giảm sub-chunking
        if len(chunk_pairs) > 5000:  # Tăng threshold lên cao
            safe_log(f"Very large chunk detected: {len(chunk_pairs)} pairs, splitting into sub-chunks")
            # Chia với size lớn hơn để giảm số sub-chunks
            sub_chunks = [chunk_pairs[i:i+2500] for i in range(0, len(chunk_pairs), 2500)]
        else:
            sub_chunks = [chunk_pairs]

        for sub_chunk in sub_chunks:
            where_filter = build_filter(sub_chunk)

            # Xử lý tables tuần tự để tránh overhead của batching
            for tbl in table_list:
                kol_name, camp_date = parse_table_info(tbl)
                query_str = f"""
                SELECT s.shop_id, s.item_id,
                       SUM(CAST(s.total_gmv AS FLOAT64)) AS table_gmv
                FROM `beyondk-live-data.Sales_data.{tbl}` s
                WHERE {where_filter}
                GROUP BY s.shop_id, s.item_id
                HAVING SUM(CAST(s.total_gmv AS FLOAT64)) != 0
                """

                # Tối ưu retry mechanism - chỉ retry cho lỗi network/timeout
                for retry in range(MAX_RETRIES):
                    try:
                        # Tối ưu timeout và enable caching
                        job_config = bigquery.QueryJobConfig()
                        job_config.job_timeout_ms = 120000  # Giảm xuống 2 phút
                        job_config.use_query_cache = True    # Enable query caching
                        job_config.use_legacy_sql = False    # Ensure standard SQL

                        # Thử sử dụng BigQuery Storage API để tăng tốc độ
                        try:
                            df_chunk = bq_client.query(query_str, job_config=job_config).to_dataframe(
                                create_bqstorage_client=True,
                                progress_bar_type=None
                            )
                        except Exception:
                            # Fallback về REST API nếu Storage API không khả dụng
                            df_chunk = bq_client.query(query_str, job_config=job_config).to_dataframe()
                        if not df_chunk.empty:
                            df_chunk["table_name"] = tbl
                            df_chunk["kol_name"] = kol_name
                            df_chunk["campaign_date"] = camp_date
                            partial_results.append(df_chunk)
                        break  # Success, exit retry loop

                    except Exception as ex:
                        # Chỉ retry cho lỗi timeout/network
                        error_str = str(ex).lower()
                        should_retry = any(keyword in error_str for keyword in
                                         ['timeout', 'connection', 'network'])

                        if retry < MAX_RETRIES - 1 and should_retry:
                            safe_log(f"Retryable error on table {tbl} (attempt {retry + 1}/{MAX_RETRIES}): {ex}")
                            time.sleep(RETRY_DELAY)
                        else:
                            if not should_retry:
                                safe_log(f"Non-retryable error on table {tbl}: {ex}")
                            else:
                                safe_log(f"Failed to query table {tbl} after {MAX_RETRIES} attempts: {ex}")
                            break  # Exit retry loop for non-retryable errors

        # Log progress - chỉ gc sau mỗi 10 chunks để tăng tốc độ
        current_chunk = (chunk_idx // CHUNK_SIZE) + 1
        safe_log(f"Completed chunk {current_chunk}/{total_chunks}")
        if current_chunk % 10 == 0:  # Chỉ gc sau mỗi 10 chunks
            gc.collect()

    if not partial_results:
        return pd.DataFrame(columns=["shop_id", "item_id", "table_gmv", "table_name", "kol_name", "campaign_date"])

    # Concat với memory optimization
    try:
        df_all = pd.concat(partial_results, ignore_index=True)
        # Giải phóng partial_results để tiết kiệm memory
        partial_results.clear()
        gc.collect()
        return df_all
    except MemoryError:
        safe_log("Memory error during concat, trying to process in smaller batches")
        # Nếu gặp lỗi memory, concat từng phần
        result_chunks = []
        batch_size = 10
        for i in range(0, len(partial_results), batch_size):
            batch = partial_results[i:i+batch_size]
            if batch:
                result_chunks.append(pd.concat(batch, ignore_index=True))
        partial_results.clear()
        gc.collect()

        if result_chunks:
            return pd.concat(result_chunks, ignore_index=True)
        else:
            return pd.DataFrame(columns=["shop_id", "item_id", "table_gmv", "table_name", "kol_name", "campaign_date"])


def compute_gmv_aggregations(df, variation_map, agg_type):
    """
    df columns: [shop_id, item_id, table_gmv, table_name, kol_name, campaign_date]
    variation_map: (shop_id, cpid) -> [item_ids]
    agg_type: 'max', 'min', or 'avg'
    
    For each table and each canonical product:
        - Find the item with max GMV among all variations
        - Use only that item's GMV for calculations
    
    Returns a DataFrame with columns:
        [shop_id, canonical_product_uid, item_gmv, table_name, kol_name, campaign_date]
    for 'max' or 'min', we pick the row with max/min value. 
    for 'avg', we average values across all campaigns.
    """
    if df.empty:
        return pd.DataFrame()

    # Build item_to_canonical
    item_to_canonical = {}
    for (sid, cpuid), item_list in variation_map.items():
        for iid in item_list:
            item_to_canonical[(sid, iid)] = cpuid

    # Add canonical_product_uid column
    cpid_list = []
    for _, rowv in df.iterrows():
        sid = rowv["shop_id"]
        iid = rowv["item_id"]
        cpid = item_to_canonical.get((sid, iid))
        cpid_list.append(cpid)
    df["canonical_product_uid"] = cpid_list

    # For each table and canonical product, select only the item with highest GMV
    best_items_per_table = []
    for (table, shop_id, cpid), group in df.groupby(['table_name', 'shop_id', 'canonical_product_uid']):
        if not group.empty:
            # Get the item with maximum GMV in this group
            max_gmv_idx = group['table_gmv'].idxmax()
            best_item = group.loc[max_gmv_idx]
            best_items_per_table.append(best_item)
    
    # Create a new DataFrame with only the best items
    if not best_items_per_table:
        return pd.DataFrame()
    
    df_best_items = pd.DataFrame(best_items_per_table)
    
    # Now proceed with aggregation using only the best items
    df_sum_item = df_best_items.rename(columns={"table_gmv": "table_gmv_sum"})

    if agg_type == 'avg':
        df_final = (
            df_sum_item
            .groupby(["shop_id", "canonical_product_uid"], as_index=False)
            .agg(item_gmv=("table_gmv_sum", "mean"))
        )
        df_final["table_name"] = None
        df_final["kol_name"] = "N/A"
        df_final["campaign_date"] = "N/A"

    elif agg_type == 'max':
        def pick_max(grp):
            idx = grp["table_gmv_sum"].idxmax()
            return grp.loc[idx]
        df_final = (
            df_sum_item
            .groupby(["shop_id", "canonical_product_uid"], group_keys=True)
            .apply(pick_max)
            .reset_index(drop=True)
            .rename(columns={"table_gmv_sum": "item_gmv"})
        )

    elif agg_type == 'min':
        def pick_min(grp):
            idx = grp["table_gmv_sum"].idxmin()
            return grp.loc[idx]
        df_final = (
            df_sum_item
            .groupby(["shop_id", "canonical_product_uid"], group_keys=True)
            .apply(pick_min)
            .reset_index(drop=True)
            .rename(columns={"table_gmv_sum": "item_gmv"})
        )
    else:
        return pd.DataFrame()

    return df_final


def main():
    import gc
    import signal
    import sys

    # Thêm signal handler để cleanup khi bị interrupt
    def signal_handler(sig, frame):
        safe_log("Received interrupt signal, cleaning up...")
        if 'bq_client' in locals():
            try:
                bq_client.close()
            except:
                pass
        gc.collect()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # --------------------------------------------------------------------------------
    # 1) User Inputs
    # --------------------------------------------------------------------------------
    kol_input = input("Enter KOL name to filter (or 'all' for none): ").strip()
    target_kolname = None if (kol_input.lower() == 'all' or kol_input == '') else kol_input.lower()

    sheet_link = input("Google Sheet URL: ").strip()
    worksheet_name = input("Worksheet name (tab): ").strip()

    item_col_letter = input("Column letter with item IDs (e.g., B): ").strip()
    shop_col_letter = input("Column letter with shop IDs (e.g., F): ").strip()

    # ---------------------
    # GMV columns (All data)
    # ---------------------
    max_gmv_col = input("\nEnter the column letter to export MAX GMV (enter to skip): ").strip()
    max_gmv_info_col = ""
    if max_gmv_col:
        max_gmv_info_col = input("Column letter for MAX GMV campaign info (enter to skip): ").strip()

    avg_gmv_col = input("\nEnter the column letter to export AVERAGE GMV (enter to skip): ").strip()

    min_gmv_col = input("\nEnter the column letter to export MIN GMV (enter to skip): ").strip()
    min_gmv_info_col = ""
    if min_gmv_col:
        min_gmv_info_col = input("Column letter for MIN GMV campaign info (enter to skip): ").strip()

    # ---------------------
    # GMV columns (6 months)
    # ---------------------
    last_6_input = input("\nDo you want to export lastest 6 months data? (y/n) ").strip().lower()
    max_6_col, max_6_info_col = "", ""
    avg_6_col = ""
    min_6_col, min_6_info_col = "", ""
    if last_6_input == 'y':
        max_6_col = input("Enter the column letter to export MAX 6GMV (enter to skip): ").strip()
        if max_6_col:
            max_6_info_col = input("Column letter for MAX 6GMV campaign info (enter to skip): ").strip()

        avg_6_col = input("\nEnter the column letter to export AVERAGE 6GMV (enter to skip): ").strip()

        min_6_col = input("\nEnter the column letter to export MIN 6GMV (enter to skip): ").strip()
        if min_6_col:
            min_6_info_col = input("Column letter for MIN 6GMV campaign info (enter to skip): ").strip()

    # ---------------------
    # GMV columns (3 months)
    # ---------------------
    last_3_input = input("\nDo you want to export lastest 3 months data? (y/n) ").strip().lower()
    max_3_col, max_3_info_col = "", ""
    avg_3_col = ""
    min_3_col, min_3_info_col = "", ""
    if last_3_input == 'y':
        max_3_col = input("Enter the column letter to export MAX 3GMV (enter to skip): ").strip()
        if max_3_col:
            max_3_info_col = input("Column letter for MAX 3GMV campaign info (enter to skip): ").strip()

        avg_3_col = input("\nEnter the column letter to export AVERAGE 3GMV (enter to skip): ").strip()

        min_3_col = input("\nEnter the column letter to export MIN 3GMV (enter to skip): ").strip()
        if min_3_col:
            min_3_info_col = input("Column letter for MIN 3GMV campaign info (enter to skip): ").strip()

    # ---------------------
    # Lowest Final Price
    # ---------------------
    lowest_price_col_letter = input("\nEnter the column letter to export LOWEST FINAL PRICE (enter to skip): ").strip()
    price_campaign_info_col_letter = ""
    orig_price_col_letter = ""
    if lowest_price_col_letter:
        price_campaign_info_col_letter = input("Column letter for LOWEST FINAL PRICE campaign info (enter to skip): ").strip()
        orig_price_col_letter = input("Enter the column letter that contains the ORIGINAL PRICE: ").strip()

    # --------------------------------------------------------------------------------
    # 2) BigQuery & Google Sheets Setup với proper resource management
    # --------------------------------------------------------------------------------
    bq_client = None
    try:
        safe_log("Initializing BigQuery client...")
        bq_client = bigquery.Client(project='beyondk-live-data')

        safe_log("Connecting to Google Sheets...")
        gc = gspread.oauth()
        sheet = gc.open_by_url(sheet_link)
        ws = sheet.worksheet(worksheet_name)
    except Exception as e:
        safe_log(f"Error accessing Google Sheets: {e}")
        if bq_client:
            try:
                bq_client.close()
            except:
                pass
        return

    item_col_idx = col_letter_to_index(item_col_letter)
    shop_col_idx = col_letter_to_index(shop_col_letter)

    rows = ws.get_all_values()
    if len(rows) < 2:
        safe_log("No data rows in the Google Sheet.")
        return

    # Get the dimensions of the worksheet to avoid exceeding grid limits
    try:
        sheet_metadata = ws.spreadsheet.fetch_sheet_metadata()
        sheet_id = None
        for sheet in sheet_metadata['sheets']:
            if sheet['properties']['title'] == worksheet_name:
                sheet_id = sheet['properties']['sheetId']
                max_rows = sheet['properties']['gridProperties']['rowCount']
                max_columns = sheet['properties']['gridProperties']['columnCount']
                break
        
        if not sheet_id:
            safe_log("Could not determine sheet dimensions, proceeding with caution")
            max_rows = float('inf')
            max_columns = float('inf')
        else:
            safe_log(f"Sheet dimensions: {max_rows} rows × {max_columns} columns")
    except Exception as e:
        safe_log(f"Error retrieving sheet dimensions: {e}. Proceeding with caution.")
        max_rows = float('inf')
        max_columns = float('inf')

    # Helper function to check if a cell is within the sheet's dimensions
    def is_within_sheet_limits(col_letter, row_num):
        """Check if a cell is within sheet limits with better error handling"""
        try:
            if not col_letter:
                return False
            col_idx = col_letter_to_index(col_letter)
            return col_idx <= max_columns and row_num <= max_rows
        except Exception as e:
            safe_log(f"Error checking sheet limits for {col_letter}{row_num}: {e}")
            # Nếu có lỗi khi kiểm tra, giả định là trong giới hạn để tiếp tục
            return True

    # Gather all (shop_id, item_id) pairs from the sheet (excluding header)
    sheet_pairs = []
    for row in rows[1:]:
        if len(row) >= max(item_col_idx, shop_col_idx):
            val_item = row[item_col_idx - 1].strip()
            val_shop = row[shop_col_idx - 1].strip()
            if val_item.isdigit() and val_shop.isdigit():
                sheet_pairs.append((int(val_shop), int(val_item)))
    if not sheet_pairs:
        safe_log("No valid (shop_id, item_id) pairs found in the sheet.")
        return

    # --------------------------------------------------------------------------------
    # 3) Retrieve canonical_product_uid for each (shop_id, item_id)
    # --------------------------------------------------------------------------------
    unique_sheet_pairs = list(set(sheet_pairs))
    center_clauses = []
    for (sid, iid) in unique_sheet_pairs:
        center_clauses.append(f"(shop_id = {sid} AND item_id = {iid})")
    center_where = " OR ".join(center_clauses)
    query_center = f"""
    SELECT shop_id, item_id, canonical_product_uid
    FROM `beyondk-live-data.central.central_ids`
    WHERE {center_where}
    """
    try:
        center_df = bq_client.query(query_center).to_dataframe()
    except Exception as e:
        safe_log(f"Error querying central_ids for canonical_product_uid: {e}")
        return
    if center_df.empty:
        safe_log("No records in central_ids match the items from the sheet.")
        return

    orig_to_canonical = {}
    for _, rowv in center_df.iterrows():
        sid = int(rowv["shop_id"])
        iid = int(rowv["item_id"])
        cpuid = rowv["canonical_product_uid"]
        orig_to_canonical[(sid, iid)] = cpuid

    # --------------------------------------------------------------------------------
    # 4) Retrieve all item_id variations
    # --------------------------------------------------------------------------------
    shop_cpid_set = set(orig_to_canonical.values())
    if not shop_cpid_set:
        safe_log("No canonical_product_uid found at all.")
        return

    var_clauses = []
    for cpuid in shop_cpid_set:
        var_clauses.append(f"canonical_product_uid = '{cpuid}'")
    var_where = " OR ".join(var_clauses)
    query_var = f"""
    SELECT shop_id, canonical_product_uid, item_id
    FROM `beyondk-live-data.central.central_ids`
    WHERE {var_where}
    """
    try:
        var_df = bq_client.query(query_var).to_dataframe()
    except Exception as e:
        safe_log(f"Error retrieving item variations from central_ids: {e}")
        return
    if var_df.empty:
        safe_log("No item variations found in central_ids for these products.")
        return

    variation_map = defaultdict(list)
    for _, rowv in var_df.iterrows():
        sid = int(rowv["shop_id"])
        cpuid = rowv["canonical_product_uid"]
        iid = int(rowv["item_id"])
        variation_map[(sid, cpuid)].append(iid)

    # Deduplicate
    for key in variation_map:
        variation_map[key] = list(set(variation_map[key]))

    # --------------------------------------------------------------------------------
    # 5) Retrieve Sales_data table list & filter by KOL
    # --------------------------------------------------------------------------------
    if target_kolname:
        tbl_q = f"""
        SELECT table_name
        FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
        WHERE LOWER(table_name) LIKE '%_{target_kolname}'
        """
    else:
        tbl_q = "SELECT table_name FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`"

    try:
        tbl_df = bq_client.query(tbl_q).to_dataframe()
    except Exception as e:
        safe_log(f"Error retrieving Sales_data table list: {e}")
        return
    if tbl_df.empty:
        safe_log("No matching Sales_data tables found.")
        return

    table_list_all = tbl_df["table_name"].tolist()

    # Filter for last 6 months, last 3 months
    table_list_6m = filter_tables_for_completed_months(table_list_all, 6) if last_6_input == 'y' else []
    table_list_3m = filter_tables_for_completed_months(table_list_all, 3) if last_3_input == 'y' else []

    # --------------------------------------------------------------------------------
    # 6) Compute GMV aggregator needed
    # --------------------------------------------------------------------------------
    aggregator_map = defaultdict(dict)

    def fill_aggregator(df_agg, aggregator_key):
        for _, rowv in df_agg.iterrows():
            sid = rowv["shop_id"]
            cpid = rowv["canonical_product_uid"]
            gmv_val = rowv["item_gmv"]
            if "max" in aggregator_key or "min" in aggregator_key:
                cdate = rowv.get("campaign_date", "N/A")
                kname = rowv.get("kol_name", "N/A")
                info_val = f"{cdate}\n{kname}"
            else:
                info_val = ""

            aggregator_map[aggregator_key][(sid, cpid)] = {
                "value": gmv_val,
                "campaign_info": info_val
            }

    # 6.1) All data (excluding avg which now uses 6 months)
    scenario_all_aggregators = {
        "max_all": bool(max_gmv_col),
        "avg_all": False,  # AVG now uses 6 months data instead
        "min_all": bool(min_gmv_col),
    }
    if any(scenario_all_aggregators.values()):
        df_all_gmv = query_gmv_for_tables(bq_client, variation_map, table_list_all)
        if not df_all_gmv.empty:
            # max
            if scenario_all_aggregators["max_all"]:
                df_max_all = compute_gmv_aggregations(df_all_gmv, variation_map, "max")
                fill_aggregator(df_max_all, "max_all")
            # avg - removed, now handled in 6 months section
            # min
            if scenario_all_aggregators["min_all"]:
                df_min_all = compute_gmv_aggregations(df_all_gmv, variation_map, "min")
                fill_aggregator(df_min_all, "min_all")

    # 6.2) Last 6 months (now includes regular AVG GMV)
    scenario_6_aggregators = {
        "max_6": (last_6_input == 'y' and bool(max_6_col)),
        "avg_6": (last_6_input == 'y' and bool(avg_6_col)) or bool(avg_gmv_col),  # Include regular AVG GMV
        "min_6": (last_6_input == 'y' and bool(min_6_col)),
    }
    if any(scenario_6_aggregators.values()):
        df_6_gmv = query_gmv_for_tables(bq_client, variation_map, table_list_6m)
        if not df_6_gmv.empty:
            if scenario_6_aggregators["max_6"]:
                df_max_6 = compute_gmv_aggregations(df_6_gmv, variation_map, "max")
                fill_aggregator(df_max_6, "max_6")
            if scenario_6_aggregators["avg_6"]:
                df_avg_6 = compute_gmv_aggregations(df_6_gmv, variation_map, "avg")
                fill_aggregator(df_avg_6, "avg_6")
            if scenario_6_aggregators["min_6"]:
                df_min_6 = compute_gmv_aggregations(df_6_gmv, variation_map, "min")
                fill_aggregator(df_min_6, "min_6")

    # 6.3) Last 3 months
    scenario_3_aggregators = {
        "max_3": (last_3_input == 'y' and bool(max_3_col)),
        "avg_3": (last_3_input == 'y' and bool(avg_3_col)),
        "min_3": (last_3_input == 'y' and bool(min_3_col)),
    }
    if any(scenario_3_aggregators.values()):
        df_3_gmv = query_gmv_for_tables(bq_client, variation_map, table_list_3m)
        if not df_3_gmv.empty:
            if scenario_3_aggregators["max_3"]:
                df_max_3 = compute_gmv_aggregations(df_3_gmv, variation_map, "max")
                fill_aggregator(df_max_3, "max_3")
            if scenario_3_aggregators["avg_3"]:
                df_avg_3 = compute_gmv_aggregations(df_3_gmv, variation_map, "avg")
                fill_aggregator(df_avg_3, "avg_3")
            if scenario_3_aggregators["min_3"]:
                df_min_3 = compute_gmv_aggregations(df_3_gmv, variation_map, "min")
                fill_aggregator(df_min_3, "min_3")

    # --------------------------------------------------------------------------------
    # 7) Write GMV Results Back to Sheet
    # --------------------------------------------------------------------------------
    updates = []
    
    # Important: We need to handle the offset for row numbers carefully
    # The very first row is already used for headers, so our data needs to start at row 4
    row_offset = 2  # This is how many rows to add to the original row numbers
    
    # Make sure we don't exceed sheet limits when adding the offset
    # If max_rows is close to our data size plus offset, adjust the offset
    if len(rows) + row_offset > max_rows:
        safe_log(f"Adjusting row offset to fit within sheet limits of {max_rows} rows")
        row_offset = 0  # If we can't fit with offset, don't use an offset
    
    # Check if headers are within sheet limits
    valid_columns = []
    for col in [max_gmv_col, max_gmv_info_col, avg_gmv_col, min_gmv_col, min_gmv_info_col,
                max_6_col, max_6_info_col, avg_6_col, min_6_col, min_6_info_col,
                max_3_col, max_3_info_col, avg_3_col, min_3_col, min_3_info_col,
                lowest_price_col_letter, price_campaign_info_col_letter]:
        if col and is_within_sheet_limits(col, 2):  # Check up to row 2 (header)
            valid_columns.append(col)
        elif col:
            safe_log(f"Column {col} exceeds sheet's column limit of {max_columns}. Skipping.")
            # Clear invalid columns
            if col == max_gmv_col: max_gmv_col = ""
            elif col == max_gmv_info_col: max_gmv_info_col = ""
            elif col == avg_gmv_col: avg_gmv_col = ""
            elif col == min_gmv_col: min_gmv_col = ""
            elif col == min_gmv_info_col: min_gmv_info_col = ""
            elif col == max_6_col: max_6_col = ""
            elif col == max_6_info_col: max_6_info_col = ""
            elif col == avg_6_col: avg_6_col = ""
            elif col == min_6_col: min_6_col = ""
            elif col == min_6_info_col: min_6_info_col = ""
            elif col == max_3_col: max_3_col = ""
            elif col == max_3_info_col: max_3_info_col = ""
            elif col == avg_3_col: avg_3_col = ""
            elif col == min_3_col: min_3_col = ""
            elif col == min_3_info_col: min_3_info_col = ""
            elif col == lowest_price_col_letter: lowest_price_col_letter = ""
            elif col == price_campaign_info_col_letter: price_campaign_info_col_letter = ""

    # Generate headers for valid columns
    headers_row1 = []  # BKdata
    headers_row2 = []  # Actual headers
    headers_row3 = []  # Empty row
    
    # Add column headers only if they're valid
    if max_gmv_col:
        headers_row1.append({"range": f"{max_gmv_col}1", "values": [["BKdata"]]})
        headers_row2.append({"range": f"{max_gmv_col}2", "values": [["MAX GMV"]]})
        headers_row3.append({"range": f"{max_gmv_col}3", "values": [[""]]})
        if max_gmv_info_col:
            headers_row1.append({"range": f"{max_gmv_info_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{max_gmv_info_col}2", "values": [["MAX GMV info"]]})
            headers_row3.append({"range": f"{max_gmv_info_col}3", "values": [[""]]})
    
    if avg_gmv_col:
        headers_row1.append({"range": f"{avg_gmv_col}1", "values": [["BKdata"]]})
        headers_row2.append({"range": f"{avg_gmv_col}2", "values": [["AVG GMV"]]})
        headers_row3.append({"range": f"{avg_gmv_col}3", "values": [[""]]})
    
    if min_gmv_col:
        headers_row1.append({"range": f"{min_gmv_col}1", "values": [["BKdata"]]})
        headers_row2.append({"range": f"{min_gmv_col}2", "values": [["MIN GMV"]]})
        headers_row3.append({"range": f"{min_gmv_col}3", "values": [[""]]})
        if min_gmv_info_col:
            headers_row1.append({"range": f"{min_gmv_info_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{min_gmv_info_col}2", "values": [["MIN GMV info"]]})
            headers_row3.append({"range": f"{min_gmv_info_col}3", "values": [[""]]})

    if last_6_input == 'y':
        if max_6_col:
            headers_row1.append({"range": f"{max_6_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{max_6_col}2", "values": [["MAX 6GMV"]]})
            headers_row3.append({"range": f"{max_6_col}3", "values": [[""]]})
            if max_6_info_col:
                headers_row1.append({"range": f"{max_6_info_col}1", "values": [["BKdata"]]})
                headers_row2.append({"range": f"{max_6_info_col}2", "values": [["MAX 6GMV info"]]})
                headers_row3.append({"range": f"{max_6_info_col}3", "values": [[""]]})
        if avg_6_col:
            headers_row1.append({"range": f"{avg_6_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{avg_6_col}2", "values": [["AVG 6GMV"]]})
            headers_row3.append({"range": f"{avg_6_col}3", "values": [[""]]})
        if min_6_col:
            headers_row1.append({"range": f"{min_6_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{min_6_col}2", "values": [["MIN 6GMV"]]})
            headers_row3.append({"range": f"{min_6_col}3", "values": [[""]]})
            if min_6_info_col:
                headers_row1.append({"range": f"{min_6_info_col}1", "values": [["BKdata"]]})
                headers_row2.append({"range": f"{min_6_info_col}2", "values": [["MIN 6GMV info"]]})
                headers_row3.append({"range": f"{min_6_info_col}3", "values": [[""]]})

    if last_3_input == 'y':
        if max_3_col:
            headers_row1.append({"range": f"{max_3_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{max_3_col}2", "values": [["MAX 3GMV"]]})
            headers_row3.append({"range": f"{max_3_col}3", "values": [[""]]})
            if max_3_info_col:
                headers_row1.append({"range": f"{max_3_info_col}1", "values": [["BKdata"]]})
                headers_row2.append({"range": f"{max_3_info_col}2", "values": [["MAX 3GMV info"]]})
                headers_row3.append({"range": f"{max_3_info_col}3", "values": [[""]]})
        if avg_3_col:
            headers_row1.append({"range": f"{avg_3_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{avg_3_col}2", "values": [["AVG 3GMV"]]})
            headers_row3.append({"range": f"{avg_3_col}3", "values": [[""]]})
        if min_3_col:
            headers_row1.append({"range": f"{min_3_col}1", "values": [["BKdata"]]})
            headers_row2.append({"range": f"{min_3_col}2", "values": [["MIN 3GMV"]]})
            headers_row3.append({"range": f"{min_3_col}3", "values": [[""]]})
            if min_3_info_col:
                headers_row1.append({"range": f"{min_3_info_col}1", "values": [["BKdata"]]})
                headers_row2.append({"range": f"{min_3_info_col}2", "values": [["MIN 3GMV info"]]})
                headers_row3.append({"range": f"{min_3_info_col}3", "values": [[""]]})
    
    # Add all headers to updates
    updates.extend(headers_row1)
    updates.extend(headers_row2)
    updates.extend(headers_row3)

    total_rows_in_sheet = len(rows) - 1
    row_updates_count = 0
    
    # Calculate exactly how many rows we can process with our row offset
    max_allowed_rows = min(max_rows - row_offset, len(rows))
    safe_log(f"Processing {max_allowed_rows} rows with offset {row_offset}")
    
    # Iterate through the rows we can safely process
    for row_num, row_values in enumerate(rows[1:max_allowed_rows], start=2):
        # Adjust row_num for the new header structure
        sheet_row_num = row_num + row_offset
        
        # Extra safety check - this should never happen with our calculations above
        if sheet_row_num > max_rows:
            safe_log(f"Row {sheet_row_num} exceeds sheet's row limit of {max_rows}. Skipping.")
            break
            
        if len(row_values) >= max(item_col_idx, shop_col_idx):
            try:
                row_item = int(row_values[item_col_idx - 1].strip())
                row_shop = int(row_values[shop_col_idx - 1].strip())
            except:
                continue
            cpid = orig_to_canonical.get((row_shop, row_item))
            if not cpid:
                continue

            # Process data for valid columns only, with strict bounds checking
            # MAX GMV
            if max_gmv_col and is_within_sheet_limits(max_gmv_col, sheet_row_num):
                val = aggregator_map["max_all"].get((row_shop, cpid))
                if val:
                    updates.append({"range": f"{max_gmv_col}{sheet_row_num}", "values": [[val["value"]]]})
                    if max_gmv_info_col and is_within_sheet_limits(max_gmv_info_col, sheet_row_num):
                        updates.append({"range": f"{max_gmv_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                    row_updates_count += 1
            
            # AVG GMV (changed to use 6 months data instead of all time)
            if avg_gmv_col and is_within_sheet_limits(avg_gmv_col, sheet_row_num):
                val = aggregator_map["avg_6"].get((row_shop, cpid))
                if val:
                    updates.append({"range": f"{avg_gmv_col}{sheet_row_num}", "values": [[val["value"]]]})
                    row_updates_count += 1
            
            # MIN GMV
            if min_gmv_col and is_within_sheet_limits(min_gmv_col, sheet_row_num):
                val = aggregator_map["min_all"].get((row_shop, cpid))
                if val:
                    updates.append({"range": f"{min_gmv_col}{sheet_row_num}", "values": [[val["value"]]]})
                    if min_gmv_info_col and is_within_sheet_limits(min_gmv_info_col, sheet_row_num):
                        updates.append({"range": f"{min_gmv_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                    row_updates_count += 1

            # Last 6 months
            if last_6_input == 'y':
                if max_6_col and is_within_sheet_limits(max_6_col, sheet_row_num):
                    val = aggregator_map["max_6"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{max_6_col}{sheet_row_num}", "values": [[val["value"]]]})
                        if max_6_info_col and is_within_sheet_limits(max_6_info_col, sheet_row_num):
                            updates.append({"range": f"{max_6_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                        row_updates_count += 1
                if avg_6_col and is_within_sheet_limits(avg_6_col, sheet_row_num):
                    val = aggregator_map["avg_6"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{avg_6_col}{sheet_row_num}", "values": [[val["value"]]]})
                        row_updates_count += 1
                if min_6_col and is_within_sheet_limits(min_6_col, sheet_row_num):
                    val = aggregator_map["min_6"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{min_6_col}{sheet_row_num}", "values": [[val["value"]]]})
                        if min_6_info_col and is_within_sheet_limits(min_6_info_col, sheet_row_num):
                            updates.append({"range": f"{min_6_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                        row_updates_count += 1

            # Last 3 months
            if last_3_input == 'y':
                if max_3_col and is_within_sheet_limits(max_3_col, sheet_row_num):
                    val = aggregator_map["max_3"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{max_3_col}{sheet_row_num}", "values": [[val["value"]]]})
                        if max_3_info_col and is_within_sheet_limits(max_3_info_col, sheet_row_num):
                            updates.append({"range": f"{max_3_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                        row_updates_count += 1
                if avg_3_col and is_within_sheet_limits(avg_3_col, sheet_row_num):
                    val = aggregator_map["avg_3"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{avg_3_col}{sheet_row_num}", "values": [[val["value"]]]})
                        row_updates_count += 1
                if min_3_col and is_within_sheet_limits(min_3_col, sheet_row_num):
                    val = aggregator_map["min_3"].get((row_shop, cpid))
                    if val:
                        updates.append({"range": f"{min_3_col}{sheet_row_num}", "values": [[val["value"]]]})
                        if min_3_info_col and is_within_sheet_limits(min_3_info_col, sheet_row_num):
                            updates.append({"range": f"{min_3_info_col}{sheet_row_num}", "values": [[val["campaign_info"]]]})
                        row_updates_count += 1

    # Batch update with careful error handling và memory management
    if updates:
        import gc
        import time

        try:
            # Tối ưu batch size để tăng tốc độ tối đa
            batch_size = 3000  # Tăng lên để giảm số batch
            max_retries = 2    # Giảm retry để tăng tốc độ
            retry_delay = 0.5  # Giảm delay

            if len(updates) > batch_size:
                safe_log(f"Breaking {len(updates)} updates into batches of {batch_size}")
                total_batches = (len(updates) + batch_size - 1) // batch_size

                for i in range(0, len(updates), batch_size):
                    batch = updates[i:i+batch_size]
                    current_batch = (i // batch_size) + 1

                    # Retry mechanism cho mỗi batch
                    for retry in range(max_retries):
                        try:
                            ws.batch_update(batch)
                            safe_log(f"Processed batch {current_batch}/{total_batches}")
                            break
                        except Exception as batch_error:
                            if retry < max_retries - 1:
                                safe_log(f"Batch {current_batch} failed (attempt {retry + 1}/{max_retries}): {batch_error}")
                                time.sleep(retry_delay)
                            else:
                                safe_log(f"Batch {current_batch} failed after {max_retries} attempts: {batch_error}")
                                raise

                    # Giải phóng memory - chỉ gc sau mỗi 5 batches để tăng tốc độ
                    if current_batch % 5 == 0:
                        gc.collect()
                    time.sleep(0.1)  # Giảm delay xuống 0.1 giây
            else:
                # Retry cho single batch
                for retry in range(max_retries):
                    try:
                        ws.batch_update(updates)
                        break
                    except Exception as single_error:
                        if retry < max_retries - 1:
                            safe_log(f"Single batch update failed (attempt {retry + 1}/{max_retries}): {single_error}")
                            time.sleep(retry_delay)
                        else:
                            safe_log(f"Single batch update failed after {max_retries} attempts: {single_error}")
                            raise

            safe_log("Batch update to the Google Sheet for GMV completed successfully.")

        except Exception as e:
            safe_log(f"Error during batch update for GMV: {e}")
            # Try to provide more detailed error info
            if hasattr(e, 'args') and len(e.args) > 0:
                safe_log(f"Error details: {e.args}")
        finally:
            # Giải phóng memory
            updates.clear()
            gc.collect()

    safe_log(f"Total rows in sheet (excluding header): {total_rows_in_sheet}")
    safe_log(f"Total items updated with at least one GMV aggregator: {row_updates_count}")

    # Apply number formatting to numeric columns
    format_requests = []
    def create_number_format_request(sheet_id, col_letter, is_price_column=False):
        col_idx = col_letter_to_index(col_letter) - 1  # Convert to 0-based index
        format_type = "CURRENCY" if is_price_column else "NUMBER"
        pattern = "#,##0 \"\u20AB\"" if is_price_column else "#,##0"
        
        return {
            "repeatCell": {
                "range": {
                    "sheetId": sheet_id,
                    "startRowIndex": 3,  # Start from row 4 (0-indexed row 3)
                    "startColumnIndex": col_idx,
                    "endColumnIndex": col_idx + 1
                },
                "cell": {
                    "userEnteredFormat": {
                        "numberFormat": {
                            "type": format_type,
                            "pattern": pattern
                        }
                    }
                },
                "fields": "userEnteredFormat.numberFormat"
            }
        }
    
    numeric_columns = []
    currency_columns = []
    # GMV columns use VND format
    if max_gmv_col: currency_columns.append(max_gmv_col)
    if avg_gmv_col: currency_columns.append(avg_gmv_col)
    if min_gmv_col: currency_columns.append(min_gmv_col)
    if max_6_col: currency_columns.append(max_6_col)
    if avg_6_col: currency_columns.append(avg_6_col)
    if min_6_col: currency_columns.append(min_6_col)
    if max_3_col: currency_columns.append(max_3_col)
    if avg_3_col: currency_columns.append(avg_3_col)
    if min_3_col: currency_columns.append(min_3_col)
    # Lowest price also uses VND format
    if lowest_price_col_letter: currency_columns.append(lowest_price_col_letter)
    
    # Create format requests for all currency columns
    for col in currency_columns:
        format_requests.append(create_number_format_request(sheet_id, col, True))
    
    # Add other numeric columns if any
    for col in numeric_columns:
        format_requests.append(create_number_format_request(sheet_id, col, False))
    
    if format_requests:
        try:
            sheet.spreadsheet.batch_update({"requests": format_requests})
            safe_log(f"Applied '#,##0' number formatting to {len(currency_columns)} currency columns and {len(numeric_columns)} numeric columns")
        except Exception as e:
            safe_log(f"Error applying number formatting: {e}")

    # --------------------------------------------------------------------------------
    # 8) (Optional) Update Lowest Historical Final Price
    # --------------------------------------------------------------------------------
    if lowest_price_col_letter and orig_price_col_letter:
        # Update headers - now in row 2 with BKdata in row 1
        price_header_updates = []
        price_header_updates.append({"range": f"{lowest_price_col_letter}1", "values": [["BKdata"]]})
        price_header_updates.append({"range": f"{lowest_price_col_letter}2", "values": [["LOWEST FINAL PRICE"]]})
        price_header_updates.append({"range": f"{lowest_price_col_letter}3", "values": [[""]]})
        
        if price_campaign_info_col_letter:
            price_header_updates.append({"range": f"{price_campaign_info_col_letter}1", "values": [["BKdata"]]})
            price_header_updates.append({"range": f"{price_campaign_info_col_letter}2", "values": [["Price Campaign Info"]]})
            price_header_updates.append({"range": f"{price_campaign_info_col_letter}3", "values": [[""]]})

        try:
            ws.batch_update(price_header_updates)
            safe_log("Headers for price columns updated successfully.")
        except Exception as e:
            safe_log(f"Error updating headers for price columns: {e}")

        price_api = PriceAPI()
        orig_price_col_index0 = col_letter_to_index(orig_price_col_letter) - 1

        # Build groups for price lookup based on (shop_id, cpid, origin_price, allowed_ids_str)
        price_lookup_groups = {}
        for row_num, row_data in enumerate(rows[1:], start=2):
            if len(row_data) >= max(item_col_idx, shop_col_idx, orig_price_col_index0 + 1):
                try:
                    row_item = int(row_data[item_col_idx - 1].strip())
                    row_shop = int(row_data[shop_col_idx - 1].strip())
                except:
                    logging.debug(f"Row {row_num}: Invalid shop/item id. Skipping.")
                    continue

                # parse original price
                if orig_price_col_index0 < len(row_data):
                    orig_price_str = row_data[orig_price_col_index0].strip()
                else:
                    orig_price_str = ""
                cleaned = re.sub(r'[^0-9.]', '', orig_price_str)
                origin_price = float(cleaned) if cleaned else 0.0

                cpid = orig_to_canonical.get((row_shop, row_item))
                if not cpid:
                    continue

                allowed_ids = variation_map.get((row_shop, cpid), [])
                if not allowed_ids:
                    continue

                allowed_ids_str = ", ".join(str(x) for x in sorted(set(allowed_ids)))
                group_key = (row_shop, cpid, origin_price, allowed_ids_str)

                if group_key not in price_lookup_groups:
                    price_lookup_groups[group_key] = []
                price_lookup_groups[group_key].append(row_num)

        group_list = []
        for key, row_nums in price_lookup_groups.items():
            shop, cpuid, origin_price, allowed_ids_str = key
            group_list.append({
                'shop': shop,
                'cpuid': cpuid,
                'origin_price': origin_price,
                'allowed_ids_str': allowed_ids_str,
                'rows': row_nums
            })

        safe_log(f"Total price lookup groups formed: {len(group_list)}")

        partial_price_results = []
        CHUNK_SIZE_PRICE = 300
        for start_idx in range(0, len(group_list), CHUNK_SIZE_PRICE):
            chunk = group_list[start_idx : start_idx + CHUNK_SIZE_PRICE]
            conditions = []
            for grp in chunk:
                cond = (
                    f"(shop_id = {grp['shop']} "
                    f"AND origin_price = {grp['origin_price']} "
                    f"AND ((item_id IN ({grp['allowed_ids_str']})) "
                    f"OR (pitching_item_id IN ({grp['allowed_ids_str']}))))"
                )
                conditions.append(cond)
            combined_condition = " OR ".join(conditions)

            price_tables = price_api.get_price_table_list(bq_client)
            for tbl in price_tables:
                df_chunk, nan_rows = price_api.get_chunked_price_data(bq_client, combined_condition, tbl)
                if not df_chunk.empty:
                    partial_price_results.append((df_chunk, nan_rows))

        if partial_price_results:
            # Tách kết quả giá và danh sách NaN
            df_list = [df for df, _ in partial_price_results]
            nan_list = [nan for _, nan in partial_price_results if not nan.empty]
            
            price_df_all = pd.concat(df_list, ignore_index=True)
            nan_rows_all = pd.concat(nan_list, ignore_index=True) if nan_list else pd.DataFrame()
            
            group_to_price = {}
            for grp in group_list:
                shop = grp['shop']
                origin_price = grp['origin_price']
                allowed_ids_str = grp['allowed_ids_str']

                df_filtered = price_df_all[
                    (price_df_all['shop_id'] == shop) &
                    (price_df_all['origin_price'] == origin_price)
                ]
                if not df_filtered.empty:
                    idx = df_filtered['lowest_price'].idxmin()
                    rowp = df_filtered.loc[idx]
                    lowest = rowp['lowest_price']
                    table_name = rowp['table']
                    key = (shop, origin_price, allowed_ids_str)
                    group_to_price[key] = (lowest, table_name)

            # Build updates
            price_updates = []
            for grp in group_list:
                key = (grp['shop'], grp['origin_price'], grp['allowed_ids_str'])
                if key not in group_to_price:
                    continue
                lowest_price_val, table_name = group_to_price[key]
                
                # Format the price as a number, not a string with commas
                if lowest_price_val is not None:
                    cell_val = lowest_price_val
                else:
                    cell_val = "New"

                campaign_info = ""
                if table_name and price_campaign_info_col_letter:
                    campaign_info = parse_price_campaign_info(table_name)

                for rnum in grp['rows']:
                    # Adjust row number for the new header structure
                    sheet_row_num = rnum + row_offset
                    
                    # Skip if beyond sheet limits
                    if sheet_row_num > max_rows:
                        continue
                    
                    if lowest_price_col_letter and is_within_sheet_limits(lowest_price_col_letter, sheet_row_num):
                        price_updates.append({"range": f"{lowest_price_col_letter}{sheet_row_num}", "values": [[cell_val]]})
                        
                        if price_campaign_info_col_letter and is_within_sheet_limits(price_campaign_info_col_letter, sheet_row_num):
                            price_updates.append({"range": f"{price_campaign_info_col_letter}{sheet_row_num}", "values": [[campaign_info]]})

            if price_updates:
                try:
                    ws.batch_update(price_updates)
                    safe_log(
                        f"Batch update for lowest final prices and campaign info completed successfully. "
                        f"Total cells updated: {len(price_updates)}"
                    )
                except Exception as e:
                    safe_log(f"Error during batch update for price data: {e}")
            else:
                safe_log("No price updates to apply.")
        else:
            safe_log("No price data retrieved for any group.")
    else:
        safe_log("Skipping lowest final price export (one or more required inputs were left blank)")

    safe_log("=== Done! ===")


if __name__ == "__main__":
    import gc
    import sys

    try:
        main()
    except KeyboardInterrupt:
        print("\nProgram interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        # Final cleanup
        gc.collect()
