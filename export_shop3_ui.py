import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout,
                             QLabel, QLineEdit, QPushButton, QComboBox, QMessageBox,
                             QTextEdit, QTabWidget, QSizePolicy, QScrollArea, QFrame)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import pandas as pd
from google.cloud import bigquery
import gspread
import export_shop3 as shop3_logic  # Import logic core từ shop3.py
from gsheet_manager import GSheetManager
import unicodedata
from dateutil.relativedelta import relativedelta

# Hàm hỗ trợ xử lý lỗi encoding tiếng Việt
def safe_encode(text, default=''):
    """Xử lý vấn đề encoding với tiếng Việt, trả về text an toàn hoặc giá trị mặc định"""
    if text is None:
        return default
        
    if isinstance(text, str):
        try:
            # Thử chuẩn hóa Unicode
            return unicodedata.normalize('NFKD', text)
        except:
            try:
                # <PERSON><PERSON><PERSON> không thành công, thử lọc ký tự không ASCII
                return ''.join(c for c in text if ord(c) < 128)
            except:
                return default
    return str(text)

class ExportWorker(QThread):
    progress = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, func, args):
        super().__init__()
        self.func = func
        self.args = args

    def run(self):
        try:
            self.func(self.args, self.progress.emit)
            self.finished.emit(True, "")
        except Exception as e:
            self.finished.emit(False, safe_encode(str(e), "Lỗi không xác định"))

class ExportShop3Widget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Export Shop UI")
        self.setMinimumWidth(600)
        self.setMaximumHeight(650)

        # Khởi tạo các thành phần
        # Thay đổi: Dùng GSheetManager cho cả sheets và bigquery
        self.gsheet_manager = GSheetManager()
        self.gsheet_client = self.gsheet_manager.sheets
        self.bq_client = self.gsheet_manager.bigquery
        
        self.export_worker = None
        self.headers = None

        # Cấu hình cột đầu ra
        self.gmv_columns = {
            'avg': QLineEdit(),
            'max': QLineEdit(),
            'min': QLineEdit()
        }
        self.cms_columns = {
            'max': QLineEdit(),
            'avg': QLineEdit(),
            'min': QLineEdit()
        }

        self.setupUI()
        self.connectSignals()

    def setupUI(self):
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # Sheet Info Group
        sheet_group = QGroupBox("Sheet Info")
        sheet_layout = QVBoxLayout()
        sheet_layout.setSpacing(5)

        url_layout = QHBoxLayout()
        self.sheet_url_edit = QLineEdit()
        self.load_sheet_btn = QPushButton("Load")
        self.load_sheet_btn.setMaximumWidth(60)
        url_layout.addWidget(self.sheet_url_edit)
        url_layout.addWidget(self.load_sheet_btn)

        self.worksheet_combo = QComboBox()
        self.kol_name_edit = QLineEdit()

        sheet_layout.addLayout(url_layout)
        sheet_layout.addWidget(QLabel("Select Worksheet:"))
        sheet_layout.addWidget(self.worksheet_combo)
        sheet_layout.addWidget(QLabel("KOL Name:"))
        sheet_layout.addWidget(self.kol_name_edit)
        sheet_group.setLayout(sheet_layout)

        # Export Settings Group - Combined group for Shop ID, GMV, and CMS
        export_group = QGroupBox("Export Settings")
        export_layout = QVBoxLayout()
        export_layout.setSpacing(10)

        # Shop ID section
        shop_layout = QFormLayout()
        self.shop_col_edit = QLineEdit()
        shop_layout.addRow("Shop ID Col:", self.shop_col_edit)
        export_layout.addLayout(shop_layout)

        # Add separator line
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        export_layout.addWidget(line)

        # GMV section - Thay đổi thứ tự thành MAX, AVG, MIN
        gmv_layout = QFormLayout()
        gmv_layout.addRow("Max GMV Col:", self.gmv_columns['max'])
        gmv_layout.addRow("Average GMV Col:", self.gmv_columns['avg'])
        gmv_layout.addRow("Min GMV Col:", self.gmv_columns['min'])
        export_layout.addLayout(gmv_layout)

        # Add another separator line
        line2 = QFrame()
        line2.setFrameShape(QFrame.Shape.HLine)
        line2.setFrameShadow(QFrame.Shadow.Sunken)
        export_layout.addWidget(line2)

        # CMS section with updated labels - Thay đổi thứ tự thành MAX, AVG, MIN
        cms_layout = QFormLayout()
        cms_layout.addRow("Max CMS (%):", self.cms_columns['max'])
        cms_layout.addRow("Avg CMS (%):", self.cms_columns['avg'])
        cms_layout.addRow("Min CMS (%):", self.cms_columns['min'])
        export_layout.addLayout(cms_layout)

        export_group.setLayout(export_layout)

        # Add groups to main layout
        main_layout.addWidget(sheet_group)
        main_layout.addWidget(export_group)
        main_layout.addStretch()

        # Run button
        run_layout = QHBoxLayout()
        self.run_button = QPushButton("Start Export")
        self.run_button.setMinimumHeight(35)
        self.run_button.setStyleSheet("""
            QPushButton {
                background-color: #0d6efd;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0b5ed7;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.run_button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        run_layout.addWidget(self.run_button)
        main_layout.addLayout(run_layout)

        # Log area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(100)
        main_layout.addWidget(self.log_text)

    def connectSignals(self):
        self.load_sheet_btn.clicked.connect(self.loadSheets)
        self.run_button.clicked.connect(self.runExport)
        self.worksheet_combo.currentIndexChanged.connect(self.onWorksheetSelected)

    def log(self, message):
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

    def loadSheets(self):
        try:
            sheet_url = self.sheet_url_edit.text().strip()
            if not sheet_url:
                raise ValueError("Vui lòng nhập URL Google Sheet")

            self.log("Đang tải danh sách worksheet...")
            # Thay đổi: Dùng gsheet_manager.open_sheet thay vì gọi trực tiếp
            sheet, error = self.gsheet_manager.open_sheet(sheet_url)
            if error:
                raise ValueError(error)
                
            self.worksheet_combo.clear()
            worksheet_names = [ws.title for ws in sheet.worksheets()]
            self.worksheet_combo.addItems(worksheet_names)

            # Determine default worksheet based on sheet and worksheet names
            sheet_title = sheet.title.lower()
            default_idx = 0

            if "pool brand" in sheet_title or "timeline" in sheet_title:
                # Look for worksheet containing "pool brand" or "brand pool"
                for idx, name in enumerate(worksheet_names):
                    if any(term in name.lower() for term in ["pool brand", "brand pool"]):
                        default_idx = idx
                        break
            else:
                # Original behavior - look for "Deal list"
                try:
                    default_idx = worksheet_names.index("Deal list")
                except ValueError:
                    default_idx = 0

            self.worksheet_combo.setCurrentIndex(default_idx)
            
            # Load worksheet data for header detection
            self.loadWorksheetData(sheet.worksheet(worksheet_names[default_idx]))
            
            self.log(f"Đã tải danh sách worksheet thành công và chọn sheet: {worksheet_names[default_idx]}")

        except Exception as e:
            self.log(f"Lỗi khi tải worksheet: {str(e)}")
            QMessageBox.warning(self, "Lỗi", str(e))

    def loadWorksheetData(self, worksheet):
        """Load and process worksheet data"""
        try:
            # For Deal list:
            # Row 1: Main header
            # Row 2: Column mapping header (used for detection)
            # Row 3: Additional info header
            # Row 4+: Data rows
            self.mapping_row = 2 if worksheet.title.lower() == "deal list" else 1
            
            # Get mapping headers from row 2 for Deal list, row 1 for others
            self.headers = worksheet.row_values(self.mapping_row)
            self.detectColumns()
            self.log("Worksheet data loaded successfully")
            
        except Exception as e:
            error_msg = f"Error loading worksheet data: {str(e)}"
            self.log(error_msg)
            QMessageBox.warning(self, "Error", error_msg)

    def detectColumns(self):
        """Auto detect columns based on header content"""
        try:
            if not self.headers:
                return

            # Column mapping definition
            column_map = {
                'shop': {
                    'exact': [
                        'shop id', 
                        'shopid', 
                        'shop_id',
                        '3. shop id của shop trên shopee',  # Add full header text
                        'shop id của shop trên shopee'  # Add variant without number
                    ],
                    'contains': [
                        'cửa hàng',
                        'shop id của shop',  # Add partial match
                        'shop id trên shopee'  # Add another variant
                    ]
                },
                'avg_gmv': {
                    'exact': ['average gmv', 'avg gmv'],
                    'contains': ['gmv trung bình', 'gmv tb']
                },
                'max_gmv': {
                    'exact': ['max gmv'],
                    'contains': ['gmv cao nhất', 'gmv max']
                },
                'min_gmv': {
                    'exact': ['min gmv'],
                    'contains': ['gmv thấp nhất', 'gmv min']
                },
                'max_cms': {
                    'exact': ['max cms (%)', 'cms max (%)'],
                    'contains': ['cms cao nhất (%)']
                },
                'avg_cms': {
                    'exact': ['average cms (%)', 'avg cms (%)', 'cms avg (%)'],
                    'contains': ['cms trung bình (%)']
                },
                'min_cms': {
                    'exact': ['min cms (%)', 'cms min (%)'],
                    'contains': ['cms thấp nhất (%)']
                }
            }

            # Map field names to UI elements
            field_to_element = {
                'shop': self.shop_col_edit,
                'avg_gmv': self.gmv_columns['avg'],
                'max_gmv': self.gmv_columns['max'],
                'min_gmv': self.gmv_columns['min'],
                'max_cms': self.cms_columns['max'],
                'avg_cms': self.cms_columns['avg'],
                'min_cms': self.cms_columns['min']
            }

            # Process each header with improved logging
            for col_idx, header in enumerate(self.headers):
                header_text = str(header).lower().strip()
                
                for field_id, patterns in column_map.items():
                    matched = False
                    match_type = None
                    
                    # Check exact matches first
                    if header_text in patterns['exact']:
                        matched = True
                        match_type = 'exact'
                    # Then check contains matches
                    elif any(pattern in header_text for pattern in patterns['contains']):
                        matched = True
                        match_type = 'contains'
                        
                    if matched:
                        col_letter = chr(65 + col_idx) if col_idx < 26 else f"A{chr(65 + col_idx - 26)}"
                        field_to_element[field_id].setText(col_letter)
                        self.log(f"Detected column '{field_id}' in column {col_letter} "
                                f"(match type: {match_type}, header: {header_text})")
                        break

        except Exception as e:
            self.log(f"Error detecting columns: {str(e)}")

    def onWorksheetSelected(self, index):
        """Handle worksheet selection changed"""
        if not self.worksheet_combo.count():
            return
            
        try:
            sheet_url = self.sheet_url_edit.text().strip()
            # Thay đổi: Dùng gsheet_manager.open_sheet thay vì gọi trực tiếp 
            sheet, error = self.gsheet_manager.open_sheet(sheet_url)
            if error:
                raise ValueError(error)
                
            worksheet = sheet.get_worksheet(index)
            self.loadWorksheetData(worksheet)
            
        except Exception as e:
            self.log(f"Error changing worksheet: {str(e)}")

    def getExportArgs(self):
        args = {
            'sheet_url': self.sheet_url_edit.text().strip(),
            'worksheet_name': self.worksheet_combo.currentText(),
            'kol_name': self.kol_name_edit.text().strip() or 'all',
            'shop_col': self.shop_col_edit.text().strip(),
            'avg_gmv_col': self.gmv_columns['avg'].text().strip() or None,
            'max_gmv_col': self.gmv_columns['max'].text().strip() or None,
            'min_gmv_col': self.gmv_columns['min'].text().strip() or None,
            'max_cms_col': self.cms_columns['max'].text().strip() or None,
            'avg_cms_col': self.cms_columns['avg'].text().strip() or None,
            'min_cms_col': self.cms_columns['min'].text().strip() or None,
        }

        # Validate required fields
        if not args['sheet_url']:
            raise ValueError("URL Google Sheet là bắt buộc")
        if not args['worksheet_name']:
            raise ValueError("Tên worksheet là bắt buộc")
        if not args['shop_col']:
            raise ValueError("Cột Shop ID là bắt buộc")

        return args

    def runExport(self):
        try:
            self.run_button.setEnabled(False)
            self.log("Bắt đầu quá trình export...")
            args = self.getExportArgs()

            self.export_worker = ExportWorker(self.executeExport, args)
            self.export_worker.progress.connect(self.log)
            self.export_worker.finished.connect(self.onExportFinished)
            self.export_worker.start()
        except Exception as e:
            self.log(f"Lỗi khi thiết lập export: {str(e)}")
            self.run_button.setEnabled(True)

    def executeExport(self, args, progress_callback):
        progress_callback("Khởi tạo kết nối Google Sheet...")
        # Thay đổi: Dùng gsheet_manager.open_sheet thay vì gọi trực tiếp
        sheet, error = self.gsheet_manager.open_sheet(args['sheet_url'])
        if error:
            progress_callback(f"Lỗi khi mở Google Sheet: {error}")
            return
            
        worksheet = sheet.worksheet(args['worksheet_name'])

        shop_col_idx = shop3_logic.col_letter_to_index(args['shop_col'])

        # Kiểm tra và làm mới BigQuery client nếu cần
        try:
            # Thực hiện một truy vấn đơn giản để kiểm tra kết nối
            progress_callback("Kiểm tra kết nối BigQuery...")
            _ = self.bq_client.query("SELECT 1").result()
        except Exception as auth_error:
            # Nếu lỗi xác thực, thử tải lại client
            progress_callback("Phát hiện lỗi xác thực BigQuery, đang thử kết nối lại...")
            self.bq_client = self.gsheet_manager.refresh_bigquery_client()
            if not self.bq_client:
                raise ValueError("Không thể kết nối lại BigQuery. Vui lòng khởi động lại ứng dụng và xác thực lại.")

        # Truy xuất dữ liệu GMV (6 tháng gần nhất thay vì toàn thời gian)
        progress_callback("Đang truy xuất dữ liệu GMV (6 tháng gần nhất)...")
        target_kolname = None if args['kol_name'].lower() == 'all' else args['kol_name']
        if target_kolname:
            table_names_query = f"""
                SELECT table_name
                FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
                WHERE table_name LIKE '%_{target_kolname}'
            """
        else:
            table_names_query = """
                SELECT table_name
                FROM `beyondk-live-data.Sales_data.INFORMATION_SCHEMA.TABLES`
            """
        table_names_df = self.bq_client.query(table_names_query).to_dataframe()
        if (table_names_df.empty):
            progress_callback("Không tìm thấy bảng campaign phù hợp.")
            df_gmv = pd.DataFrame(columns=['shop_id', 'shop_name', 'avg_gmv', 'max_gmv', 'min_gmv'])
        else:
            # Filter tables to last 6 months only
            from datetime import datetime

            def parse_sales_table_date(table_name):
                """Parse date from table name format: YYYY_MM_DD_KOLname"""
                parts = table_name.split("_")
                if len(parts) < 4:
                    return None
                yyyy, mm, dd = parts[0], parts[1], parts[2]
                try:
                    return datetime(year=int(yyyy), month=int(mm), day=int(dd))
                except ValueError:
                    return None

            def filter_tables_for_6_months(table_names):
                """Filter tables to last 6 months from current date"""
                from dateutil.relativedelta import relativedelta

                now = datetime.now()
                # Calculate 6 months ago from current month
                start_of_range = now.replace(day=1) - relativedelta(months=5)  # 6 months including current
                end_of_range = now

                results = []
                for name in table_names:
                    d = parse_sales_table_date(name)
                    if d is not None and (start_of_range <= d <= end_of_range):
                        results.append(name)
                return results

            # Filter to last 6 months only
            all_tables = table_names_df['table_name'].tolist()
            filtered_tables = filter_tables_for_6_months(all_tables)

            if not filtered_tables:
                progress_callback("Không tìm thấy bảng campaign trong 6 tháng gần nhất.")
                df_gmv = pd.DataFrame(columns=['shop_id', 'shop_name', 'avg_gmv', 'max_gmv', 'min_gmv'])
            else:
                progress_callback(f"Sử dụng {len(filtered_tables)} bảng từ 6 tháng gần nhất để tính AVG GMV")
                union_queries = [
                    f"""
                    SELECT shop_id, shop_name, SUM(total_gmv) AS campaign_gmv
                    FROM `beyondk-live-data.Sales_data.{table}`
                    GROUP BY shop_id, shop_name
                    """
                    for table in filtered_tables
                ]
                union_query = " UNION ALL ".join(union_queries)
                final_query = f"""
                WITH union_data AS (
                    {union_query}
                )
                SELECT
                    shop_id,
                    shop_name,
                    AVG(campaign_gmv) AS avg_gmv,
                    MAX(campaign_gmv) AS max_gmv,
                    MIN(campaign_gmv) AS min_gmv
                FROM union_data
                GROUP BY shop_id, shop_name
                ORDER BY shop_id
                """
                df_gmv = self.bq_client.query(final_query).to_dataframe()

        # Truy xuất dữ liệu CMS
        do_cms = any([args['max_cms_col'], args['avg_cms_col'], args['min_cms_col']])
        if do_cms:
            progress_callback("Đang truy xuất dữ liệu CMS...")
            if target_kolname:
                price_table_names_query = f"""
                    SELECT table_name
                    FROM `beyondk-live-data.price_data.INFORMATION_SCHEMA.TABLES`
                    WHERE table_name LIKE '%_{target_kolname}'
                """
            else:
                price_table_names_query = """
                    SELECT table_name
                    FROM `beyondk-live-data.price_data.INFORMATION_SCHEMA.TABLES`
                """
            price_table_names_df = self.bq_client.query(price_table_names_query).to_dataframe()
            if price_table_names_df.empty:
                progress_callback("Không tìm thấy bảng giá phù hợp.")
                df_price = pd.DataFrame(columns=['shop_id', 'max_cms', 'avg_cms', 'min_cms'])
            else:
                union_queries_price = [
                    f"SELECT shop_id, cms FROM `beyondk-live-data.price_data.{table}`"
                    for table in price_table_names_df['table_name']
                ]
                price_union_query = " UNION ALL ".join(union_queries_price)
                price_final_query = f"""
                WITH union_price_data AS (
                    {price_union_query}
                )
                SELECT
                    CAST(shop_id AS STRING) AS shop_id,
                    MAX(cms) AS max_cms,
                    AVG(cms) AS avg_cms,
                    MIN(cms) AS min_cms
                FROM union_price_data
                GROUP BY shop_id
                ORDER BY shop_id
                """
                df_price = self.bq_client.query(price_final_query).to_dataframe()
        else:
            df_price = pd.DataFrame(columns=['shop_id', 'max_cms', 'avg_cms', 'min_cms'])

        # Gộp dữ liệu GMV & CMS
        df_gmv['shop_id'] = df_gmv['shop_id'].astype(str)
        df_merged = pd.merge(df_gmv, df_price, on='shop_id', how='outer')

        # Tạo từ điển ánh xạ
        metrics_mapping = {}
        for _, row in df_merged.iterrows():
            shop_id_str = str(row['shop_id']).strip()
            metrics_mapping[shop_id_str] = {
                'avg_gmv': row.get('avg_gmv'),
                'max_gmv': row.get('max_gmv'),
                'min_gmv': row.get('min_gmv'),
                'max_cms': row.get('max_cms'),
                'avg_cms': row.get('avg_cms'),
                'min_cms': row.get('min_cms')
            }

        # Cập nhật Google Sheet
        progress_callback("Đang cập nhật Google Sheet...")
        all_values = worksheet.get_all_values()
        total_shops_in_sheet = len(all_values) - 1
        updates = []

        # Tiêu đề cột với % cho CMS
        if args['avg_gmv_col']:
            updates.append({"range": f"{args['avg_gmv_col']}1", "values": [["AVERAGE GMV"]]})
        if args['max_gmv_col']:
            updates.append({"range": f"{args['max_gmv_col']}1", "values": [["MAX GMV"]]})
        if args['min_gmv_col']:
            updates.append({"range": f"{args['min_gmv_col']}1", "values": [["MIN GMV"]]})
        if args['max_cms_col']:
            updates.append({"range": f"{args['max_cms_col']}1", "values": [["MAX CMS (%)"]]})
        if args['avg_cms_col']:
            updates.append({"range": f"{args['avg_cms_col']}1", "values": [["AVERAGE CMS (%)"]]})
        if args['min_cms_col']:
            updates.append({"range": f"{args['min_cms_col']}1", "values": [["MIN CMS (%)"]]})

        # Dữ liệu
        counts = {'avg_gmv': 0, 'max_gmv': 0, 'min_gmv': 0, 'max_cms': 0, 'avg_cms': 0, 'min_cms': 0}
        for row_number, row in enumerate(all_values[1:], start=2):
            if len(row) >= shop_col_idx:
                shop_id_in_sheet = row[shop_col_idx - 1].strip()
                if shop_id_in_sheet in metrics_mapping:
                    data = metrics_mapping[shop_id_in_sheet]
                    if args['avg_gmv_col']:
                        val = shop3_logic.safe_json_value(data['avg_gmv'])
                        updates.append({"range": f"{args['avg_gmv_col']}{row_number}", "values": [[val]]})
                        counts['avg_gmv'] += 1
                    if args['max_gmv_col']:
                        val = shop3_logic.safe_json_value(data['max_gmv'])
                        updates.append({"range": f"{args['max_gmv_col']}{row_number}", "values": [[val]]})
                        counts['max_gmv'] += 1
                    if args['min_gmv_col']:
                        val = shop3_logic.safe_json_value(data['min_gmv'])
                        updates.append({"range": f"{args['min_gmv_col']}{row_number}", "values": [[val]]})
                        counts['min_gmv'] += 1
                    if args['max_cms_col']:
                        raw_cms = shop3_logic.safe_json_value(data['max_cms'])
                        final_cms = shop3_logic.fix_commission(raw_cms) if raw_cms != "" else ""
                        updates.append({"range": f"{args['max_cms_col']}{row_number}", "values": [[final_cms]]})
                        counts['max_cms'] += 1
                    if args['avg_cms_col']:
                        raw_cms = shop3_logic.safe_json_value(data['avg_cms'])
                        final_cms = shop3_logic.fix_commission(raw_cms) if raw_cms != "" else ""
                        updates.append({"range": f"{args['avg_cms_col']}{row_number}", "values": [[final_cms]]})
                        counts['avg_cms'] += 1
                    if args['min_cms_col']:
                        raw_cms = shop3_logic.safe_json_value(data['min_cms'])
                        final_cms = shop3_logic.fix_commission(raw_cms) if raw_cms != "" else ""
                        updates.append({"range": f"{args['min_cms_col']}{row_number}", "values": [[final_cms]]})
                        counts['min_cms'] += 1

        if updates:
            worksheet.batch_update(updates)
            progress_callback("Đã cập nhật Google Sheet thành công")

        # Định dạng cột
        if args['avg_gmv_col']:
            worksheet.format(f"{args['avg_gmv_col']}:{args['avg_gmv_col']}", {"numberFormat": {"type": "CURRENCY", "pattern": "#,##0 \"\u20AB\""}})
        if args['max_gmv_col']:
            worksheet.format(f"{args['max_gmv_col']}:{args['max_gmv_col']}", {"numberFormat": {"type": "CURRENCY", "pattern": "#,##0 \"\u20AB\""}})
        if args['min_gmv_col']:
            worksheet.format(f"{args['min_gmv_col']}:{args['min_gmv_col']}", {"numberFormat": {"type": "CURRENCY", "pattern": "#,##0 \"\u20AB\""}})
        if args['max_cms_col']:
            worksheet.format(f"{args['max_cms_col']}:{args['max_cms_col']}", {"numberFormat": {"type": "NUMBER", "pattern": "0.0%"}})
        if args['avg_cms_col']:
            worksheet.format(f"{args['avg_cms_col']}:{args['avg_cms_col']}", {"numberFormat": {"type": "NUMBER", "pattern": "0.0%"}})
        if args['min_cms_col']:
            worksheet.format(f"{args['min_cms_col']}:{args['min_cms_col']}", {"numberFormat": {"type": "NUMBER", "pattern": "0.0%"}})

        # Báo cáo tóm tắt
        progress_callback("\nBáo cáo tóm tắt:")
        progress_callback(f"Tổng số shop trong Google Sheet (trừ header): {total_shops_in_sheet}")
        if args['avg_gmv_col']:
            progress_callback(f"Cập nhật AVERAGE GMV: {counts['avg_gmv']}")
        if args['max_gmv_col']:
            progress_callback(f"Cập nhật MAX GMV: {counts['max_gmv']}")
        if args['min_gmv_col']:
            progress_callback(f"Cập nhật MIN GMV: {counts['min_gmv']}")
        if args['max_cms_col']:
            progress_callback(f"Cập nhật MAX CMS: {counts['max_cms']}")
        if args['avg_cms_col']:
            progress_callback(f"Cập nhật AVG CMS: {counts['avg_cms']}")
        if args['min_cms_col']:
            progress_callback(f"Cập nhật MIN CMS: {counts['min_cms']}")
        overall_updates = max(counts.values())
        not_found = total_shops_in_sheet - overall_updates
        progress_callback(f"Shop không có dữ liệu lịch sử: {not_found}")

        progress_callback("Quá trình export hoàn tất!")

    def onExportFinished(self, success, error_message):
        self.run_button.setEnabled(True)
        if success:
            self.log("Export hoàn tất thành công.")
        else:
            self.log(f"Export thất bại: {error_message}")

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    window = ExportShop3Widget()
    window.show()
    sys.exit(app.exec())